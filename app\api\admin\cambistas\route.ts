import { type NextRequest, NextResponse } from "next/server"
import mysql from 'mysql2/promise'
import bcrypt from 'bcryptjs'
import { initializeDatabase, executeQuery, executeQuerySingle } from "@/lib/database-config"

// Força renderização dinâmica para evitar erro de build estático
export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  let connection
  try {
    console.log("🔍 Buscando cambistas...")

    try {
      // Tentar conectar ao banco real
      connection = await mysql.createConnection({
        host: 'localhost',
        user: 'root',
        password: '',
        database: 'sistema-bolao-top',
        timeout: 5000
      })

      // Buscar cambistas do banco
      const [cambistas] = await connection.execute(
        'SELECT id, nome, email, telefone, endereco, cpf_cnpj, status, porcentagem_comissao, data_cadastro FROM usuarios WHERE tipo = ?',
        ['cambista']
      )

      console.log(`📋 Encontrados ${cambistas.length} cambistas no banco`)

      const stats = {
        ativos: cambistas.filter(c => c.status === 'ativo').length,
        totalVendas: 0,
        vendasHoje: 0,
        totalComissoes: 0
      }

      return NextResponse.json({
        cambistas: cambistas,
        stats: stats,
      })

    } catch (dbError) {
      console.log("⚠️ Erro de conexão com banco, usando dados de teste:", dbError.message)

      // Fallback para dados de teste
      const cambistas = [
        {
          id: 22,
          nome: "João Silva",
          email: "<EMAIL>",
          telefone: "(11) 99999-9999",
          endereco: "Rua das Flores, 123",
          cpf_cnpj: "123.456.789-00",
          status: "ativo",
          porcentagem_comissao: 10,
          data_cadastro: new Date().toISOString()
        }
      ]

      const stats = {
        ativos: 1,
        totalVendas: 5,
        vendasHoje: 2,
        totalComissoes: 25.50
      }

      return NextResponse.json({
        cambistas: cambistas,
        stats: stats,
      })
    }

  } catch (error) {
    console.error("❌ Erro geral:", error)
    return NextResponse.json(
      {
        error: "Erro interno do servidor",
        message: error.message,
        cambistas: [],
        stats: { ativos: 0, totalVendas: 0, vendasHoje: 0, totalComissoes: 0 },
      },
      { status: 500 }
    )
  } finally {
    if (connection) {
      try {
        await connection.end()
      } catch (e) {
        console.log("Erro ao fechar conexão:", e.message)
      }
    }
  }
}

// Função auxiliar para criar cambista
async function createCambista(data: any) {
  try {
    await initializeDatabase()

    // Verificar se email já existe
    const existingUser = await executeQuerySingle(
      "SELECT id FROM usuarios WHERE email = ?",
      [data.email]
    )

    if (existingUser) {
      throw new Error("Email já cadastrado no sistema")
    }

    // Hash da senha
    const senhaHash = await bcrypt.hash(data.senha, 10)

    // Inserir novo cambista
    const result = await executeQuery(`
      INSERT INTO usuarios (
        nome, email, telefone, endereco, cpf_cnpj,
        senha_hash, tipo, status, porcentagem_comissao,
        data_cadastro, data_atualizacao
      ) VALUES (?, ?, ?, ?, ?, ?, 'cambista', 'ativo', 10, NOW(), NOW())
    `, [
      data.nome,
      data.email,
      data.telefone,
      data.endereco,
      data.cpf_cnpj,
      senhaHash
    ])

    return result.insertId
  } catch (error) {
    console.error("Erro ao criar cambista:", error)
    throw error
  }
}

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Validação dos campos obrigatórios
    if (!data.nome || !data.email || !data.telefone || !data.senha) {
      return NextResponse.json(
        {
          error: "Campos obrigatórios: nome, email, telefone e senha",
          success: false,
        },
        {
          status: 400,
          headers: {
            "Content-Type": "application/json",
          },
        },
      )
    }

    // Validação de email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(data.email)) {
      return NextResponse.json(
        {
          error: "Email inválido",
          success: false,
        },
        {
          status: 400,
          headers: {
            "Content-Type": "application/json",
          },
        },
      )
    }

    // Validação de CPF/CNPJ (básica)
    if (data.cpf_cnpj && data.cpf_cnpj.length > 0) {
      const cpfCnpjClean = data.cpf_cnpj.replace(/[^\d]/g, "")
      if (cpfCnpjClean.length !== 11 && cpfCnpjClean.length !== 14) {
        return NextResponse.json(
          {
            error: "CPF deve ter 11 dígitos ou CNPJ deve ter 14 dígitos",
            success: false,
          },
          {
            status: 400,
            headers: {
              "Content-Type": "application/json",
            },
          },
        )
      }
    }

    console.log("Dados recebidos para criar cambista:", {
      nome: data.nome,
      email: data.email,
      telefone: data.telefone,
      endereco: data.endereco || "Não informado",
      cpf_cnpj: data.cpf_cnpj || "Não informado",
    })

    const cambistaId = await createCambista({
      nome: data.nome,
      email: data.email,
      telefone: data.telefone,
      endereco: data.endereco || null,
      cpf_cnpj: data.cpf_cnpj || null,
      senha: data.senha,
    })

    console.log("Cambista criado com sucesso, ID:", cambistaId)

    return NextResponse.json(
      {
        success: true,
        id: cambistaId,
        message: "Cambista criado com sucesso",
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    )
  } catch (error) {
    console.error("Erro ao criar cambista:", error)

    if ((error as Error).message === "Email já cadastrado no sistema") {
      return NextResponse.json(
        {
          error: (error as Error).message,
          success: false,
        },
        {
          status: 400,
          headers: {
            "Content-Type": "application/json",
          },
        },
      )
    }

    return NextResponse.json(
      {
        error: "Erro interno do servidor",
        success: false,
      },
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      },
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    await initializeDatabase()

    const body = await request.json()
    const { id, nome, email, telefone, endereco, cpf_cnpj, status, senha, porcentagem_comissao } = body

    console.log("✏️ Atualizando cambista:", { id, nome, email, status, porcentagem_comissao })

    // Validações básicas
    if (!id) {
      return NextResponse.json(
        { error: "ID é obrigatório" },
        { status: 400 }
      )
    }

    // Verificar se cambista existe
    const existing = await executeQuerySingle(
      "SELECT id FROM usuarios WHERE id = ? AND tipo = 'cambista'",
      [id]
    )

    if (!existing) {
      return NextResponse.json(
        { error: "Cambista não encontrado" },
        { status: 404 }
      )
    }

    // Preparar dados para atualização
    const porcentagem = parseFloat(porcentagem_comissao) || 0

    // Preparar query de atualização
    let updateQuery = `
      UPDATE usuarios SET
        nome = ?,
        email = ?,
        telefone = ?,
        endereco = ?,
        cpf_cnpj = ?,
        status = ?,
        porcentagem_comissao = ?,
        data_atualizacao = NOW()
      WHERE id = ? AND tipo = 'cambista'
    `
    let updateParams = [nome, email, telefone, endereco, cpf_cnpj, status, porcentagem, id]

    // Se senha foi fornecida, incluir na atualização
    if (senha && senha.trim() !== '') {
      const senhaHash = await bcrypt.hash(senha, 10)
      updateQuery = `
        UPDATE usuarios SET
          nome = ?,
          email = ?,
          telefone = ?,
          endereco = ?,
          cpf_cnpj = ?,
          status = ?,
          porcentagem_comissao = ?,
          senha_hash = ?,
          data_atualizacao = NOW()
        WHERE id = ? AND tipo = 'cambista'
      `
      updateParams = [nome, email, telefone, endereco, cpf_cnpj, status, porcentagem, senhaHash, id]
    }

    const result = await executeQuery(updateQuery, updateParams)

    console.log("✅ Cambista atualizado com sucesso:", { id, porcentagem_comissao: porcentagem, affected: result.affectedRows })

    return NextResponse.json({
      success: true,
      message: "Cambista atualizado com sucesso",
      porcentagem_comissao: porcentagem
    })

  } catch (error: any) {
    console.error("❌ Erro ao atualizar cambista:", error)

    return NextResponse.json(
      {
        error: "Erro interno do servidor",
        message: error.message || "Não foi possível atualizar o cambista"
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await initializeDatabase()

    const { searchParams } = new URL(request.url)
    const id = searchParams.get("id")

    if (!id) {
      return NextResponse.json(
        { error: "ID do cambista é obrigatório" },
        { status: 400 }
      )
    }

    console.log("🗑️ Deletando cambista:", id)

    // Verificar se cambista existe
    const existingCambista = await executeQuerySingle(
      "SELECT id FROM usuarios WHERE id = ? AND tipo = 'cambista'",
      [id]
    )

    if (!existingCambista) {
      return NextResponse.json(
        { error: "Cambista não encontrado" },
        { status: 404 }
      )
    }

    // Deletar cambista (soft delete - marcar como inativo)
    await executeQuery(
      "UPDATE usuarios SET status = 'inativo', data_atualizacao = NOW() WHERE id = ? AND tipo = 'cambista'",
      [id]
    )

    console.log("✅ Cambista desativado com sucesso:", id)

    return NextResponse.json({
      success: true,
      message: "Cambista desativado com sucesso"
    })

  } catch (error: any) {
    console.error("❌ Erro ao desativar cambista:", error)

    return NextResponse.json(
      {
        error: "Erro interno do servidor",
        message: "Não foi possível desativar o cambista"
      },
      { status: 500 }
    )
  }
}
