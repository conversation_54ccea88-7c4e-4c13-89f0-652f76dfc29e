import { NextRequest, NextResponse } from 'next/server'

// Forçar rota dinâmica
export const dynamic = 'force-dynamic'

const FOOTBALL_API_URL = process.env.FOOTBALL_API_URL || 'https://api.football-data.org/v4'
const FOOTBALL_API_TOKEN = process.env.FOOTBALL_API_TOKEN || 'cbeb9f19b15e4252b3f9d3375fefcfcc'

// Competições brasileiras e internacionais principais
const COMPETITIONS = {
  // Brasileiras
  'BSA': { id: 2013, name: 'Brasileirão Série A', country: 'Brasil' },
  'BSB': { id: 2014, name: 'Brasileirão Série B', country: 'Brasil' },
  'CSB': { id: 2012, name: 'Copa do Brasil', country: 'Brasil' },
  
  // Europeias principais
  'PL': { id: 2021, name: 'Premier League', country: 'Inglaterra' },
  'PD': { id: 2014, name: 'La Liga', country: 'Espanha' },
  'SA': { id: 2019, name: 'Serie A', country: 'Itália' },
  'BL1': { id: 2002, name: 'Bundesliga', country: 'Alemanha' },
  'FL1': { id: 2015, name: 'Ligue 1', country: 'França' },
  
  // Competições internacionais
  'CL': { id: 2001, name: 'UEFA Champions League', country: 'Europa' },
  'EL': { id: 2018, name: 'UEFA Europa League', country: 'Europa' },
  'WC': { id: 2000, name: 'FIFA World Cup', country: 'Mundial' },
  'EC': { id: 2018, name: 'UEFA European Championship', country: 'Europa' },
  'CLI': { id: 2152, name: 'Copa América', country: 'América do Sul' }
}

async function fetchFootballData(endpoint: string) {
  try {
    console.log(`🌐 Buscando dados: ${FOOTBALL_API_URL}${endpoint}`)
    
    const response = await fetch(`${FOOTBALL_API_URL}${endpoint}`, {
      headers: {
        'X-Auth-Token': FOOTBALL_API_TOKEN,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    console.log(`✅ Dados recebidos: ${endpoint}`)
    return data
  } catch (error) {
    console.error(`❌ Erro ao buscar ${endpoint}:`, error)
    throw error
  }
}

function formatMatch(match: any, competitionInfo: any) {
  return {
    id: match.id,
    competition: competitionInfo.code || 'unknown',
    competitionName: competitionInfo.name || 'Competição',
    homeTeam: {
      id: match.homeTeam.id,
      name: match.homeTeam.name,
      shortName: match.homeTeam.shortName || match.homeTeam.tla || match.homeTeam.name,
      tla: match.homeTeam.tla,
      crest: match.homeTeam.crest || '/images/team-placeholder.png'
    },
    awayTeam: {
      id: match.awayTeam.id,
      name: match.awayTeam.name,
      shortName: match.awayTeam.shortName || match.awayTeam.tla || match.awayTeam.name,
      tla: match.awayTeam.tla,
      crest: match.awayTeam.crest || '/images/team-placeholder.png'
    },
    utcDate: match.utcDate,
    status: match.status,
    matchday: match.matchday,
    stage: match.stage,
    score: match.score,
    odds: match.odds || null,
    venue: match.venue || null
  }
}

export async function GET(request: NextRequest) {
  try {
    // Evitar execução durante build
    if (process.env.NODE_ENV === 'production' && !request.url.includes('localhost')) {
      if (!request.url) {
        return NextResponse.json({ matches: [], message: "Build mode" })
      }
    }

    console.log("🔍 Buscando partidas...")

    const { searchParams } = new URL(request.url)
    const competitions = searchParams.get('competitions')?.split(',') || ['PL', 'PD', 'SA', 'BL1', 'FL1', 'CL']
    const dateFrom = searchParams.get('dateFrom') || new Date().toISOString().split('T')[0]
    const dateTo = searchParams.get('dateTo') || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    const status = searchParams.get('status') || 'SCHEDULED,LIVE,IN_PLAY'

    console.log('🏆 Buscando partidas reais da Football Data API:', {
      competitions,
      dateFrom,
      dateTo,
      status
    })

    const allMatches: any[] = []
    const competitionData: any[] = []

    // Buscar partidas para cada competição
    for (const compCode of competitions) {
      try {
        const compInfo = COMPETITIONS[compCode as keyof typeof COMPETITIONS]
        if (!compInfo) {
          console.log(`⚠️ Competição não encontrada: ${compCode}`)
          continue
        }

        // Buscar informações da competição
        const competitionInfo = await fetchFootballData(`/competitions/${compCode}`)
        competitionData.push(competitionInfo)

        // Buscar partidas da competição
        const matchesData = await fetchFootballData(
          `/competitions/${compCode}/matches?dateFrom=${dateFrom}&dateTo=${dateTo}&status=${status}`
        )

        if (matchesData.matches && matchesData.matches.length > 0) {
          const formattedMatches = matchesData.matches.map((match: any) => 
            formatMatch(match, competitionInfo)
          )
          allMatches.push(...formattedMatches)
          console.log(`✅ ${formattedMatches.length} partidas encontradas para ${competitionInfo.name}`)
        }

        // Delay para evitar rate limit
        await new Promise(resolve => setTimeout(resolve, 200))

      } catch (error) {
        console.error(`❌ Erro ao buscar partidas da competição ${compCode}:`, error)
        // Continuar com outras competições mesmo se uma falhar
      }
    }

    // Ordenar partidas por data
    allMatches.sort((a, b) => new Date(a.utcDate).getTime() - new Date(b.utcDate).getTime())

    console.log(`🎯 Total de partidas encontradas: ${allMatches.length}`)

    return NextResponse.json({
      success: true,
      matches: allMatches,
      competitions: competitionData,
      total: allMatches.length,
      filters: {
        competitions,
        dateFrom,
        dateTo,
        status
      }
    })

  } catch (error) {
    console.error('❌ Erro ao buscar partidas:', error)

    return NextResponse.json({
      success: false,
      error: 'Erro ao buscar dados da API',
      matches: [],
      competitions: [],
      total: 0,
      fallback: true
    })
  }
}
