const mysql = require('mysql2/promise')

async function addPixOrderIdColumn() {
  let connection
  
  try {
    console.log('🔧 Conectando ao MySQL...')
    
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '',
      database: 'sistema-bolao-top',
      charset: 'utf8mb4'
    })
    
    console.log('✅ Conectado ao MySQL')
    
    // Verificar se a coluna já existe
    console.log('🔍 Verificando se coluna pix_order_id já existe...')
    const [columns] = await connection.execute(`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'sistema-bolao-top' 
      AND TABLE_NAME = 'bilhetes' 
      AND COLUMN_NAME = 'pix_order_id'
    `)
    
    if (columns.length > 0) {
      console.log('✅ Coluna pix_order_id já existe')
      return
    }
    
    console.log('➕ Adicionando coluna pix_order_id...')
    
    // Adicionar a coluna
    await connection.execute(`
      ALTER TABLE bilhetes 
      ADD COLUMN pix_order_id VARCHAR(100) NULL 
      AFTER transaction_id
    `)
    
    console.log('✅ Coluna pix_order_id adicionada com sucesso')
    
    // Adicionar índice para performance
    console.log('📊 Criando índice...')
    await connection.execute(`
      ALTER TABLE bilhetes 
      ADD INDEX idx_pix_order_id (pix_order_id)
    `)
    
    console.log('✅ Índice idx_pix_order_id criado com sucesso')
    
    // Verificar estrutura da tabela
    console.log('📋 Verificando estrutura da tabela...')
    const [tableStructure] = await connection.execute(`DESCRIBE bilhetes`)
    
    console.log('📊 Estrutura atual da tabela bilhetes:')
    tableStructure.forEach(column => {
      console.log(`  - ${column.Field}: ${column.Type} ${column.Null === 'YES' ? 'NULL' : 'NOT NULL'}`)
    })
    
  } catch (error) {
    console.error('❌ Erro:', error.message)
    throw error
  } finally {
    if (connection) {
      await connection.end()
      console.log('🔌 Conexão MySQL fechada')
    }
  }
}

// Executar
addPixOrderIdColumn()
  .then(() => {
    console.log('🎉 Script concluído com sucesso!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Script falhou:', error)
    process.exit(1)
  })
