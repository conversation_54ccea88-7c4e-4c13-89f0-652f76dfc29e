import { NextRequest, NextResponse } from "next/server"
import { executeQuery, initializeDatabase } from "@/lib/database-config"

export async function POST(request: NextRequest) {
  try {
    await initializeDatabase()

    const body = await request.json()
    const {
      user_id,
      apostas,
      valor,
      qr_code_pix,
      transaction_id,
      client_name,
      client_email,
      client_document,
      cambista_id
    } = body

    console.log("🎫 Iniciando criação de bilhete:", {
      user_id,
      valor,
      apostas: apostas?.length,
      client_name,
      client_email,
      client_document,
      cambista_id,
      transaction_id
    })



    // Validações aprimoradas
    if (!user_id) {
      console.log("❌ Validação falhou: user_id obrigatório")
      return NextResponse.json({ error: "Usuário deve estar logado para criar bilhetes" }, { status: 400 })
    }

    // Verificar se o usuário existe no banco de dados
    console.log("🔍 Verificando se usuário existe no banco:", user_id)
    const usuarioExiste = await executeQuery('SELECT id FROM usuarios WHERE id = ?', [user_id])
    console.log("📊 Resultado da verificação de usuário:", usuarioExiste)

    if (!usuarioExiste || usuarioExiste.length === 0) {
      console.log("❌ Validação falhou: user_id não existe no banco:", user_id)
      return NextResponse.json({ error: "Usuário não encontrado no sistema" }, { status: 400 })
    }

    console.log("✅ Usuário validado com sucesso:", usuarioExiste[0])

    if (!apostas || !Array.isArray(apostas) || apostas.length === 0) {
      console.log("❌ Validação falhou: apostas inválidas")
      return NextResponse.json({ error: "Pelo menos uma aposta é obrigatória" }, { status: 400 })
    }

    if (!valor || valor <= 0) {
      console.log("❌ Validação falhou: valor inválido")
      return NextResponse.json({ error: "Valor deve ser maior que zero" }, { status: 400 })
    }

    // Validação dos dados do cliente

    // Aplicar valores padrão se os dados estiverem vazios
    const finalClientName = client_name && client_name.trim() !== "" ? client_name.trim() : "Usuário Padrão"
    const finalClientEmail = client_email && client_email.trim() !== "" ? client_email.trim() : "<EMAIL>"
    const finalClientDocument = client_document && client_document.trim() !== "" ? client_document.trim() : "00000000000"



    // Gerar código único do bilhete mais robusto
    const timestamp = Date.now()
    const randomSuffix = Math.random().toString(36).substring(2, 8).toUpperCase()
    const codigoBilhete = `BLT${timestamp}${user_id}${randomSuffix}`

    console.log("🔢 Código do bilhete gerado:", codigoBilhete)

    // Inserir bilhete com tratamento de erro específico
    try {
      const bilheteResult = await executeQuery(`
        INSERT INTO bilhetes (
          codigo, usuario_id, cambista_id, usuario_nome, usuario_email, usuario_cpf,
          valor_total, quantidade_apostas, status, qr_code_pix, transaction_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pendente', ?, ?)
      `, [
        codigoBilhete,
        user_id,
        cambista_id || null,
        finalClientName,
        finalClientEmail,
        finalClientDocument,
        valor,
        apostas.length,
        qr_code_pix || null,
        transaction_id || null
      ])

      const bilheteId = (bilheteResult as any).insertId
      console.log("✅ Bilhete inserido no banco com ID:", bilheteId)

      // Inserir apostas individuais se necessário (futuro)
      console.log("📊 Apostas do bilhete recebidas:", apostas)
      console.log("📊 Apostas formatadas:", apostas.map(a => ({
        jogo_id: a.jogo_id,
        palpite: a.palpite,
        jogo_nome: a.jogo_nome,
        odd: a.odd
      })))

      const bilheteCompleto = {
        id: bilheteId,
        codigo: codigoBilhete,
        valor,
        status: "pendente",
        qr_code_pix,
        transaction_id,
        apostas_count: apostas.length,
        finalClientName,
        finalClientEmail,
        created_at: new Date().toISOString()
      }

      console.log("🎉 Bilhete criado com sucesso:", bilheteCompleto)

      return NextResponse.json({
        success: true,
        message: "Bilhete criado com sucesso",
        bilhete: bilheteCompleto
      })

    } catch (dbError) {
      console.error("❌ Erro específico do banco de dados:", dbError)

      // Verificar se é erro de coluna inexistente
      if (dbError instanceof Error && dbError.message.includes("Unknown column")) {
        console.log("🔄 Tentando INSERT simplificado...")

        const bilheteResultSimple = await executeQuery(`
          INSERT INTO bilhetes (codigo, usuario_id, valor_total, status, qr_code_pix, transaction_id)
          VALUES (?, ?, ?, 'pendente', ?, ?)
        `, [codigoBilhete, user_id, valor, qr_code_pix || null, transaction_id || null])

        const bilheteId = (bilheteResultSimple as any).insertId

        return NextResponse.json({
          success: true,
          message: "Bilhete criado com sucesso (modo simplificado)",
          bilhete: {
            id: bilheteId,
            codigo: codigoBilhete,
            valor,
            status: "pendente",
            qr_code_pix,
            transaction_id,
            apostas_count: apostas.length
          }
        })
      }

      throw dbError
    }

  } catch (error) {
    console.error("❌ Erro geral ao criar bilhete:", error)
    console.error("❌ Stack trace completo:", error)

    return NextResponse.json({
      error: "Erro interno do servidor",
      message: "Não foi possível criar o bilhete",
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
