"use client"

import { useState, useEffect } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { toast } from "sonner"
import { 
  User, 
  DollarSign, 
  TrendingUp, 
  FileText, 
  Eye, 
  EyeOff, 
  Loader2,
  LogOut,
  Printer,
  Calendar,
  BarChart3,
  Wallet,
  Receipt,
  Settings,
  Download
} from "lucide-react"

interface Cambista {
  id: number
  nome: string
  email: string
  telefone: string
  tipo: string
  porcentagem_comissao?: number
}

interface BilheteVendido {
  id: number
  codigo: string
  usuario_nome: string
  usuario_email: string
  valor_total: number
  quantidade_apostas: number
  status: string
  data_criacao: string
  comissao_valor?: number
}

interface DashboardStats {
  bilhetes_vendidos: number
  valor_total_vendas: number
  comissao_total: number
  bilhetes_pagos: number
  bilhetes_pendentes: number
  vendas_hoje: number
  comissao_hoje: number
}

export default function CambistaDashboard() {
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [cambista, setCambista] = useState<Cambista | null>(null)
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [loginForm, setLoginForm] = useState({ email: "", senha: "" })
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [bilhetes, setBilhetes] = useState<BilheteVendido[]>([])
  const [loadingData, setLoadingData] = useState(false)
  const [filtroStatus, setFiltroStatus] = useState("todos")
  const router = useRouter()

  useEffect(() => {
    // Verificar se já está logado
    const savedCambista = localStorage.getItem("cambista_dashboard")
    if (savedCambista) {
      const cambistaData = JSON.parse(savedCambista)
      setCambista(cambistaData)
      setIsLoggedIn(true)
      loadDashboardData(cambistaData.id)
    }
  }, [])

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Usar a mesma API de login do frontend principal
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: loginForm.email,
          senha: loginForm.senha
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Erro no login')
      }

      if (!result.success) {
        throw new Error(result.message || 'Erro no login')
      }

      // Verificar se é cambista
      if (result.user.tipo !== "cambista") {
        throw new Error("Acesso negado. Esta área é exclusiva para cambistas.")
      }

      console.log("✅ Cambista logado na dashboard:", result.user)

      // Salvar dados do cambista
      const cambistaData = {
        id: result.user.id,
        nome: result.user.nome,
        email: result.user.email,
        telefone: result.user.telefone,
        tipo: result.user.tipo,
        porcentagem_comissao: result.user.porcentagem_comissao || 10
      }

      localStorage.setItem("cambista_dashboard", JSON.stringify(cambistaData))
      setCambista(cambistaData)
      setIsLoggedIn(true)
      
      toast.success(`🏪 Bem-vindo à sua dashboard, ${result.user.nome}!`)
      
      // Carregar dados da dashboard
      await loadDashboardData(result.user.id)

    } catch (error: any) {
      console.error("❌ Erro no login:", error)
      toast.error(error.message || "Erro ao fazer login")
    } finally {
      setIsLoading(false)
    }
  }

  const loadDashboardData = async (cambistaId: number) => {
    setLoadingData(true)
    try {
      // Carregar estatísticas e bilhetes do cambista
      const [statsResponse, bilhetesResponse] = await Promise.all([
        fetch(`/api/cambistas/${cambistaId}/stats`),
        fetch(`/api/cambistas/${cambistaId}/bilhetes`)
      ])

      if (statsResponse.ok) {
        const statsData = await statsResponse.json()
        setStats(statsData)
      }

      if (bilhetesResponse.ok) {
        const bilhetesData = await bilhetesResponse.json()
        setBilhetes(bilhetesData.bilhetes || [])
      }

    } catch (error) {
      console.error("❌ Erro ao carregar dados:", error)
      toast.error("Erro ao carregar dados da dashboard")
    } finally {
      setLoadingData(false)
    }
  }

  const loadBilhetesComFiltro = async (status: string = "todos") => {
    if (!cambista) return

    setLoadingData(true)
    try {
      const url = `/api/cambistas/${cambista.id}/bilhetes${status !== "todos" ? `?status=${status}` : ""}`
      const response = await fetch(url)

      if (response.ok) {
        const data = await response.json()
        setBilhetes(data.bilhetes || [])
      }
    } catch (error) {
      console.error("❌ Erro ao carregar bilhetes:", error)
      toast.error("Erro ao carregar bilhetes")
    } finally {
      setLoadingData(false)
    }
  }

  const handleFiltroChange = (novoFiltro: string) => {
    setFiltroStatus(novoFiltro)
    loadBilhetesComFiltro(novoFiltro)
  }

  const handleLogout = () => {
    localStorage.removeItem("cambista_dashboard")
    setCambista(null)
    setIsLoggedIn(false)
    setStats(null)
    setBilhetes([])
    toast.success("Logout realizado com sucesso!")
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pendente":
        return <Badge variant="secondary">Pendente</Badge>
      case "pago":
      case "aprovado":
        return <Badge className="bg-green-600">Pago</Badge>
      case "cancelado":
        return <Badge variant="destructive">Cancelado</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const handleReimprimirBilhete = async (bilhete: BilheteVendido) => {
    try {
      console.log("🖨️ Reimprimindo bilhete:", bilhete.codigo)

      // Criar conteúdo do bilhete para impressão térmica 57mm
      const bilheteContent = `
==============================
      BOLÃO BRASIL
==============================
Bilhete: ${bilhete.codigo}
Data: ${new Date(bilhete.data_criacao).toLocaleDateString('pt-BR')} ${new Date(bilhete.data_criacao).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })}
------------------------------
Cliente: ${bilhete.usuario_nome}
Email: ${bilhete.usuario_email}
------------------------------
Apostas: ${bilhete.quantidade_apostas}
Valor: R$ ${bilhete.valor_total.toFixed(2)}
------------------------------
CAMBISTA: ${cambista?.nome || 'Sistema'}
Status: ${bilhete.status.toUpperCase()} ${bilhete.status === 'pago' ? '✅' : '⏳'}
${bilhete.status === 'pago' && bilhete.comissao_valor ? `Comissão: R$ ${bilhete.comissao_valor.toFixed(2)}` : ''}
==============================
    ${bilhete.status === 'pago' ? 'Boa sorte!' : 'Aguardando pagamento'}
==============================`

      console.log("📄 Conteúdo do bilhete:", bilheteContent)

      // Criar download do bilhete
      const blob = new Blob([bilheteContent], { type: "text/plain" })
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `bilhete_reimpresso_${bilhete.codigo}.txt`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      toast.success("🖨️ Bilhete reimpresso com sucesso!")

    } catch (error) {
      console.error("❌ Erro ao reimprimir bilhete:", error)
      toast.error("Erro ao reimprimir bilhete")
    }
  }

  // Tela de login
  if (!isLoggedIn) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
              <User className="h-8 w-8 text-white" />
            </div>
            <CardTitle className="text-2xl font-bold">Dashboard Cambista</CardTitle>
            <CardDescription>Acesse sua área de gestão de vendas</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={loginForm.email}
                  onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="senha">Senha</Label>
                <div className="relative">
                  <Input
                    id="senha"
                    type={showPassword ? "text" : "password"}
                    placeholder="Digite sua senha"
                    value={loginForm.senha}
                    onChange={(e) => setLoginForm({ ...loginForm, senha: e.target.value })}
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                  </Button>
                </div>
              </div>
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                Entrar
              </Button>
            </form>

            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600 mb-2">Dados para teste:</p>
              <p className="text-xs text-gray-500">Email: <EMAIL></p>
              <p className="text-xs text-gray-500">Senha: 123456</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Dashboard principal
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                <User className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-gray-900">Dashboard Cambista</h1>
                <p className="text-sm text-gray-500">{cambista?.nome}</p>
              </div>
            </div>
            <Button variant="outline" onClick={handleLogout}>
              <LogOut className="h-4 w-4 mr-2" />
              Sair
            </Button>
          </div>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {loadingData ? (
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : (
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Visão Geral</TabsTrigger>
              <TabsTrigger value="bilhetes">Bilhetes</TabsTrigger>
              <TabsTrigger value="comissoes">Comissões</TabsTrigger>
              <TabsTrigger value="configuracoes">Configurações</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-6">
              {/* Cards de estatísticas */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Bilhetes Vendidos</CardTitle>
                    <FileText className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{stats?.bilhetes_vendidos || 0}</div>
                    <p className="text-xs text-muted-foreground">
                      {stats?.bilhetes_pagos || 0} pagos, {stats?.bilhetes_pendentes || 0} pendentes
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Vendas Totais</CardTitle>
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatCurrency(stats?.valor_total_vendas || 0)}</div>
                    <p className="text-xs text-muted-foreground">
                      Hoje: {formatCurrency(stats?.vendas_hoje || 0)}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Comissões</CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatCurrency(stats?.comissao_total || 0)}</div>
                    <p className="text-xs text-muted-foreground">
                      Hoje: {formatCurrency(stats?.comissao_hoje || 0)}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Taxa de Comissão</CardTitle>
                    <Wallet className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{cambista?.porcentagem_comissao || 10}%</div>
                    <p className="text-xs text-muted-foreground">
                      Por bilhete vendido
                    </p>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="bilhetes">
              <Card>
                <CardHeader>
                  <CardTitle>Bilhetes Vendidos</CardTitle>
                  <CardDescription>
                    Histórico de todos os bilhetes vendidos por você
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {/* Filtros */}
                  <div className="flex gap-2 mb-4">
                    <Button
                      variant={filtroStatus === "todos" ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleFiltroChange("todos")}
                    >
                      Todos
                    </Button>
                    <Button
                      variant={filtroStatus === "pago" ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleFiltroChange("pago")}
                    >
                      Pagos
                    </Button>
                    <Button
                      variant={filtroStatus === "pendente" ? "default" : "outline"}
                      size="sm"
                      onClick={() => handleFiltroChange("pendente")}
                    >
                      Pendentes
                    </Button>
                  </div>
                  <div className="rounded-md border">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Código</TableHead>
                          <TableHead>Cliente</TableHead>
                          <TableHead>Valor</TableHead>
                          <TableHead>Apostas</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Data</TableHead>
                          <TableHead>Ações</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {bilhetes.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={7} className="text-center py-8">
                              Nenhum bilhete vendido ainda
                            </TableCell>
                          </TableRow>
                        ) : (
                          bilhetes.map((bilhete) => (
                            <TableRow key={bilhete.id}>
                              <TableCell className="font-mono text-sm">
                                {bilhete.codigo}
                              </TableCell>
                              <TableCell>
                                <div>
                                  <div className="font-medium">{bilhete.usuario_nome}</div>
                                  <div className="text-sm text-gray-500">{bilhete.usuario_email}</div>
                                </div>
                              </TableCell>
                              <TableCell>{formatCurrency(bilhete.valor_total)}</TableCell>
                              <TableCell>{bilhete.quantidade_apostas}</TableCell>
                              <TableCell>{getStatusBadge(bilhete.status)}</TableCell>
                              <TableCell>
                                {new Date(bilhete.data_criacao).toLocaleDateString("pt-BR")}
                              </TableCell>
                              <TableCell>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleReimprimirBilhete(bilhete)}
                                  title="Reimprimir bilhete"
                                >
                                  <Printer className="h-4 w-4" />
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="comissoes">
              <div className="space-y-6">
                {/* Resumo de Comissões */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Comissão Total</CardTitle>
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{formatCurrency(stats?.comissao_total || 0)}</div>
                      <p className="text-xs text-muted-foreground">
                        De {formatCurrency(stats?.valor_total_vendas || 0)} em vendas
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Comissão Hoje</CardTitle>
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{formatCurrency(stats?.comissao_hoje || 0)}</div>
                      <p className="text-xs text-muted-foreground">
                        {stats?.vendas_hoje || 0} vendas hoje
                      </p>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                      <CardTitle className="text-sm font-medium">Taxa Atual</CardTitle>
                      <TrendingUp className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold">{cambista?.porcentagem_comissao || 10}%</div>
                      <p className="text-xs text-muted-foreground">
                        Por bilhete pago
                      </p>
                    </CardContent>
                  </Card>
                </div>

                {/* Detalhamento por Bilhete */}
                <Card>
                  <CardHeader>
                    <CardTitle>Detalhamento de Comissões</CardTitle>
                    <CardDescription>
                      Comissões por bilhete vendido e pago
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Bilhete</TableHead>
                            <TableHead>Cliente</TableHead>
                            <TableHead>Valor Bilhete</TableHead>
                            <TableHead>Comissão</TableHead>
                            <TableHead>Data</TableHead>
                            <TableHead>Status</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {bilhetes.filter(b => b.status === 'pago').length === 0 ? (
                            <TableRow>
                              <TableCell colSpan={6} className="text-center py-8">
                                Nenhuma comissão gerada ainda
                              </TableCell>
                            </TableRow>
                          ) : (
                            bilhetes
                              .filter(b => b.status === 'pago')
                              .map((bilhete) => (
                                <TableRow key={bilhete.id}>
                                  <TableCell className="font-mono text-sm">
                                    {bilhete.codigo}
                                  </TableCell>
                                  <TableCell>
                                    <div className="font-medium">{bilhete.usuario_nome}</div>
                                  </TableCell>
                                  <TableCell>{formatCurrency(bilhete.valor_total)}</TableCell>
                                  <TableCell className="font-bold text-green-600">
                                    {formatCurrency(bilhete.comissao_valor || 0)}
                                  </TableCell>
                                  <TableCell>
                                    {new Date(bilhete.data_criacao).toLocaleDateString("pt-BR")}
                                  </TableCell>
                                  <TableCell>
                                    <Badge className="bg-green-600">Pago</Badge>
                                  </TableCell>
                                </TableRow>
                              ))
                          )}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="configuracoes">
              <div className="space-y-6">
                {/* Informações da Conta */}
                <Card>
                  <CardHeader>
                    <CardTitle>Informações da Conta</CardTitle>
                    <CardDescription>
                      Seus dados como cambista
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium">Nome</Label>
                        <p className="text-sm text-gray-600">{cambista?.nome}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Email</Label>
                        <p className="text-sm text-gray-600">{cambista?.email}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Telefone</Label>
                        <p className="text-sm text-gray-600">{cambista?.telefone || 'Não informado'}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium">Taxa de Comissão</Label>
                        <p className="text-sm text-gray-600">{cambista?.porcentagem_comissao || 10}%</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Ações Rápidas */}
                <Card>
                  <CardHeader>
                    <CardTitle>Ações Rápidas</CardTitle>
                    <CardDescription>
                      Ferramentas úteis para cambistas
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Button
                        variant="outline"
                        className="h-20 flex flex-col gap-2"
                        onClick={() => window.open('http://localhost:3000', '_blank')}
                      >
                        <Receipt className="h-6 w-6" />
                        <span>Acessar Sistema Principal</span>
                      </Button>

                      <Button
                        variant="outline"
                        className="h-20 flex flex-col gap-2"
                        onClick={() => loadBilhetesComFiltro("todos")}
                      >
                        <Download className="h-6 w-6" />
                        <span>Atualizar Dados</span>
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Instruções */}
                <Card>
                  <CardHeader>
                    <CardTitle>Como Usar</CardTitle>
                    <CardDescription>
                      Instruções para usar o sistema como cambista
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3 text-sm">
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">1</div>
                        <div>
                          <p className="font-medium">Faça login no sistema principal</p>
                          <p className="text-gray-600">Use as mesmas credenciais em localhost:3000</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">2</div>
                        <div>
                          <p className="font-medium">Venda bilhetes normalmente</p>
                          <p className="text-gray-600">Todos os bilhetes serão automaticamente associados a você</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">3</div>
                        <div>
                          <p className="font-medium">Impressão automática</p>
                          <p className="text-gray-600">Bilhetes pagos são impressos automaticamente</p>
                        </div>
                      </div>
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-xs font-bold">4</div>
                        <div>
                          <p className="font-medium">Acompanhe suas vendas</p>
                          <p className="text-gray-600">Use esta dashboard para ver estatísticas e comissões</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </div>
  )
}
