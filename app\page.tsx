"use client"

import { useState, useEffect } from "react"
import "../styles/payment-success.css"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { QRCodeComponent } from "@/components/ui/qr-code"
import TeamLogo from "@/components/TeamLogo"
import CompetitionEmblem from "@/components/CompetitionEmblem"
import {
  ChevronLeft,
  ChevronRight,
  Loader2,
  Eye,
  EyeOff,
  Mail,
  Lock,
  User,
  Phone,
  Printer,
  CheckCircle,
  XCircle,
  LogOut,
  Receipt,
  CreditCard,
  ChevronDown,
  Calendar,
  DollarSign,
  UserPlus,
} from "lucide-react"
import { formatCPF, validateCPF, getCPFErrorMessage } from "@/lib/cpf-utils"
import { toast } from "sonner"

interface Competition {
  id: string
  name: string
  color: string
  headerColor: string
}

interface Team {
  id: number
  name: string
  shortName: string
  crest: string
}

interface Match {
  id: number
  competition: string
  competitionName?: string
  homeTeam: Team
  awayTeam: Team
  utcDate: string
  status: string
}

interface Aposta {
  match_id: number
  resultado: "casa" | "empate" | "fora" | null
}

interface BannerSlide {
  id: number
  title: string
  subtitle: string
  image: string
  color: string
  active: boolean
  order: number
}

interface LoginForm {
  email: string
  senha: string
}

interface RegisterForm {
  nome: string
  email: string
  telefone: string
  cpf: string
  senha: string
  confirmarSenha: string
}

interface PixResponse {
  qr_code_value: string
  qrcode_image: string
  expiration_datetime: string
  status: string
  transaction_id: string
  order_id: string
}

interface BilheteAposta {
  id: string
  codigo?: string
  data: string
  hora: string
  apostas: Array<{
    jogo: string
    resultado: string
  }>
  valor: number
  valor_total?: number
  status: "pendente" | "ganhou" | "perdeu" | "pago"
  qr_code_pix?: string
  transaction_id?: string
}

interface UserData {
  id: number
  nome: string
  email: string
  telefone?: string
  cpf?: string
  tipo: string
}

interface Pagamento {
  id: string
  data: string
  valor: number
  status: "pendente" | "aprovado" | "rejeitado"
  metodo: string
  bilhete_id: string
}

export default function BolaoPage() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [apostas, setApostas] = useState<Aposta[]>([])
  const [matches, setMatches] = useState<Match[]>([])
  const [banners, setBanners] = useState<BannerSlide[]>([])
  const [loading, setLoading] = useState(true)
  const [showLoginDialog, setShowLoginDialog] = useState(false)
  const [showRegisterDialog, setShowRegisterDialog] = useState(false)
  const [showPaymentDialog, setShowPaymentDialog] = useState(false)
  const [showBilheteDialog, setShowBilheteDialog] = useState(false)
  const [apostasEncerradas, setApostasEncerradas] = useState(false)
  const [bolaoFinalizado, setBolaoFinalizado] = useState(false)
  const [ranking, setRanking] = useState<any[]>([])
  const [bolaoAtual, setBolaoAtual] = useState<any>(null)
  const [showUserBilhetes, setShowUserBilhetes] = useState(false)
  const [showUserPagamentos, setShowUserPagamentos] = useState(false)
  const [showUserProfile, setShowUserProfile] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [showPaymentSuccess, setShowPaymentSuccess] = useState(false)
  const [paymentSuccessData, setPaymentSuccessData] = useState<any>(null)
  const [loginLoading, setLoginLoading] = useState(false)
  const [registerLoading, setRegisterLoading] = useState(false)
  const [paymentLoading, setPaymentLoading] = useState(false)
  const [printingBilhete, setPrintingBilhete] = useState(false)
  const [error, setError] = useState("")
  const [cpfError, setCpfError] = useState("")
  const [pixData, setPixData] = useState<PixResponse | null>(null)
  const [bilhete, setBilhete] = useState<BilheteAposta | null>(null)
  const [selectedBilhete, setSelectedBilhete] = useState<BilheteAposta | null>(null)
  const [user, setUser] = useState<UserData | null>(null)
  const [userBilhetes, setUserBilhetes] = useState<BilheteAposta[]>([])
  const [userPagamentos, setUserPagamentos] = useState<Pagamento[]>([])
  const [userStats, setUserStats] = useState({
    totalApostas: 0,
    totalGasto: 0,
    totalGanho: 0,
    boloesParticipados: 0,
    taxaAcerto: 0,
    melhorPremio: 0,
  })

  const [loginForm, setLoginForm] = useState<LoginForm>({
    email: "",
    senha: "",
  })

  const [registerForm, setRegisterForm] = useState<RegisterForm>({
    nome: "",
    email: "",
    telefone: "",
    cpf: "",
    senha: "",
    confirmarSenha: "",
  })

  const [codigoAfiliado, setCodigoAfiliado] = useState<string | null>(null)
  const [valorAposta, setValorAposta] = useState<number>(25.0) // Valor padrão

  // Função para tocar som de sucesso
  const playSuccessSound = () => {
    try {
      // Criar um som de sucesso usando Web Audio API
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()

      // Criar uma sequência de notas para um som de sucesso
      const playNote = (frequency: number, duration: number, delay: number = 0) => {
        setTimeout(() => {
          const oscillator = audioContext.createOscillator()
          const gainNode = audioContext.createGain()

          oscillator.connect(gainNode)
          gainNode.connect(audioContext.destination)

          oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime)
          oscillator.type = 'sine'

          gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)
          gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration)

          oscillator.start(audioContext.currentTime)
          oscillator.stop(audioContext.currentTime + duration)
        }, delay)
      }

      // Tocar uma melodia de sucesso (Dó - Mi - Sol - Dó)
      playNote(523.25, 0.2, 0)    // Dó
      playNote(659.25, 0.2, 200)  // Mi
      playNote(783.99, 0.2, 400)  // Sol
      playNote(1046.50, 0.4, 600) // Dó oitava

    } catch (error) {
      console.log("Som não disponível:", error)
    }
  }



  useEffect(() => {
    const initializeApp = async () => {
      await loadData()
      await checkUserLogin()
      checkAffiliateCode()
    }
    initializeApp()
  }, [])

  const checkAffiliateCode = () => {
    try {
      const urlParams = new URLSearchParams(window.location.search)
      const refCode = urlParams.get('ref')

      if (refCode) {
        console.log("🔗 Código de afiliado detectado:", refCode)
        setCodigoAfiliado(refCode)

        // Armazenar no localStorage temporariamente
        localStorage.setItem('affiliate_ref', refCode)

        // Mostrar toast informativo
        toast.success(`Você está se registrando através do link de um afiliado!`)
      }
    } catch (error) {
      console.error("❌ Erro ao verificar código de afiliado:", error)
    }
  }

  const checkUserLogin = async () => {
    try {
      const userData = localStorage.getItem("user")
      if (userData) {
        const parsedUser = JSON.parse(userData)
        console.log("✅ Usuário logado:", parsedUser)
        setUser(parsedUser)
        await loadUserData(parsedUser.id)
      } else {
        console.log("❌ Nenhum usuário logado")
      }
    } catch (error) {
      console.error("❌ Erro ao verificar login:", error)
      localStorage.removeItem("user")
    }
  }

  const loadUserData = async (userId: number) => {
    try {
      console.log("🔄 Carregando dados reais do usuário:", userId)

      // Buscar bilhetes reais do banco
      const bilhetesResponse = await fetch(`/api/user/bilhetes?user_id=${userId}`)
      const bilhetesData = await bilhetesResponse.json()

      // Buscar pagamentos reais do banco
      const pagamentosResponse = await fetch(`/api/user/pagamentos?user_id=${userId}`)
      const pagamentosData = await pagamentosResponse.json()

      const bilhetes = bilhetesData.bilhetes || []
      const pagamentos = pagamentosData.pagamentos || []

      // Calcular estatísticas reais
      const totalGasto = pagamentos.reduce((sum: number, p: any) => sum + p.valor, 0)
      const bilhetesGanhos = bilhetes.filter((b: any) => b.status === "ganhou")
      const totalGanho = bilhetesGanhos.reduce((sum: number, b: any) => sum + (b.premio || 0), 0)

      setUserStats({
        totalApostas: bilhetes.length,
        totalGasto: totalGasto,
        totalGanho: totalGanho,
        boloesParticipados: bilhetes.length,
        taxaAcerto:
          bilhetes.length > 0
            ? (bilhetesGanhos.length / bilhetes.length) * 100
            : 0,
        melhorPremio: bilhetesGanhos.length > 0 ? Math.max(...bilhetesGanhos.map((b: any) => b.premio || 0)) : 0,
      })

      setUserBilhetes(bilhetes)
      setUserPagamentos(pagamentos)

      console.log("✅ Dados reais do usuário carregados:", {
        bilhetes: bilhetes.length,
        pagamentos: pagamentos.length,
        stats: { totalApostas: bilhetes.length, totalGasto, totalGanho },
      })
    } catch (error) {
      console.error("❌ Erro ao carregar dados do usuário:", error)
      // Em caso de erro, manter arrays vazios
      setUserBilhetes([])
      setUserPagamentos([])
      setUserStats({
        totalApostas: 0,
        totalGasto: 0,
        totalGanho: 0,
        boloesParticipados: 0,
        taxaAcerto: 0,
        melhorPremio: 0,
      })
    }
  }

  const checkBolaoStatus = async (bolaoId: number) => {
    try {
      const response = await fetch(`/api/boloes/${bolaoId}/status`)
      const data = await response.json()

      if (data.success) {
        setApostasEncerradas(data.apostas_encerradas)
        setBolaoFinalizado(data.bolao_finalizado)

        if (data.bolao_finalizado && data.ranking) {
          setRanking(data.ranking)
        }

        console.log("📊 Status do bolão:", {
          apostasEncerradas: data.apostas_encerradas,
          bolaoFinalizado: data.bolao_finalizado,
          ranking: data.ranking?.length || 0
        })
      }
    } catch (error) {
      console.error("❌ Erro ao verificar status do bolão:", error)
    }
  }

  const loadData = async () => {
    try {
      setLoading(true)
      console.log("🔄 Carregando dados da aplicação...")

      // Buscar apenas bolões ativos (que já incluem os jogos selecionados)
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 10000) // 10 segundos timeout

      const boloesResponse = await fetch("/api/boloes", {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!boloesResponse.ok) {
        throw new Error(`HTTP error! status: ${boloesResponse.status}`)
      }

      const boloesData = await boloesResponse.json()

      if (boloesData.success && boloesData.boloes.length > 0) {
        // Usar dados reais dos bolões
        const bolaoAtivo = boloesData.boloes[0] // Pegar o primeiro bolão ativo

        // Definir o valor da aposta do bolão ativo
        setValorAposta(parseFloat(bolaoAtivo.valor_aposta || 25.0))

        const adminBanners: BannerSlide[] = [
          {
            id: bolaoAtivo.id,
            title: "BOLÃO BRASIL",
            subtitle: "Faça suas apostas e concorra a prêmios incríveis!",
            image: bolaoAtivo.banner_image || "/placeholder.svg?height=200&width=400",
            color: "from-green-600 to-blue-600",
            active: true,
            order: 1,
          }
        ]

        // Usar apenas os jogos que foram selecionados para este bolão
        const jogosFormatados = (bolaoAtivo.jogos || []).map((jogo: any) => ({
          id: jogo.id,
          competition: (jogo.campeonato_codigo || jogo.campeonato || jogo.campeonato_nome)?.toLowerCase().replace(/\s+/g, '-') || 'campeonato',
          competitionName: jogo.campeonato || jogo.campeonato_nome || 'Campeonato',
          homeTeam: {
            id: jogo.time_casa_id,
            name: jogo.time_casa || jogo.time_casa_nome || "Time Casa",
            shortName: jogo.time_casa_curto || "TC",
            crest: jogo.time_casa_logo || "/placeholder.svg"
          },
          awayTeam: {
            id: jogo.time_fora_id,
            name: jogo.time_fora || jogo.time_fora_nome || "Time Fora",
            shortName: jogo.time_fora_curto || "TF",
            crest: jogo.time_fora_logo || "/placeholder.svg"
          },
          utcDate: jogo.data_jogo,
          status: jogo.status === "agendado" ? "SCHEDULED" : jogo.status.toUpperCase()
        }))

        console.log("🖼️ Banner do bolão:", {
          bolaoId: bolaoAtivo?.id,
          bannerImage: bolaoAtivo?.banner_image,
          bannerFinal: adminBanners[0]?.image
        })

        setBanners(adminBanners)
        setMatches(jogosFormatados)
        setBolaoAtual(bolaoAtivo)

        const apostasIniciais = jogosFormatados.map((match: any) => ({
          match_id: match.id,
          resultado: null as "casa" | "empate" | "fora" | null,
        }))
        setApostas(apostasIniciais)

        // Verificar status do bolão
        await checkBolaoStatus(bolaoAtivo.id)

        console.log("✅ Dados reais carregados:", {
          bolao: bolaoAtivo.nome,
          jogos: jogosFormatados.length,
          apostas: apostasIniciais.length,
          campeonatos: [...new Set(jogosFormatados.map((j: any) => j.competitionName))],
          jogosSelecionados: "Apenas jogos selecionados no bolão"
        })
      } else {
        // Fallback para dados mockados se não houver bolões ativos
        const adminBanners: BannerSlide[] = [
          {
            id: 1,
            title: "NENHUM BOLÃO ATIVO",
            subtitle: "Aguarde novos bolões serem criados!",
            image: "/placeholder.svg?height=200&width=400",
            color: "from-gray-600 to-gray-700",
            active: true,
            order: 1,
          },
        ]

        setBanners(adminBanners)
        setMatches([])
        setApostas([])

        console.log("⚠️ Nenhum bolão ativo encontrado")
      }
    } catch (error) {
      console.error("❌ Erro ao carregar dados:", error)

      // Fallback para dados mockados em caso de erro
      const adminBanners: BannerSlide[] = [
        {
          id: 1,
          title: "ERRO DE CONEXÃO",
          subtitle: "Verifique sua conexão com o banco de dados",
          image: "/placeholder.svg?height=200&width=400",
          color: "from-red-600 to-orange-600",
          active: true,
          order: 1,
        },
      ]

      // Carregar dados reais da API


      await new Promise((resolve) => setTimeout(resolve, 1000))

      setBanners(adminBanners.filter((b) => b.active).sort((a, b) => a.order - b.order))

      // Carregar partidas reais da Football Data API
      console.log('🏆 Carregando partidas reais da Football Data API...')
      let finalMatches: Match[] = []

      try {
        const footballResponse = await fetch('/api/football/matches?competitions=PL,PD,SA,BL1,FL1,CL&status=SCHEDULED,LIVE')
        const footballData = await footballResponse.json()

        if (footballData.success && footballData.matches.length > 0) {
          finalMatches = footballData.matches.slice(0, 20) // Limitar a 20 partidas
          console.log(`✅ ${finalMatches.length} partidas reais carregadas da Football Data API`)
        } else {
          console.log('⚠️ Nenhuma partida real encontrada, usando dados mockados')
        }
      } catch (apiError) {
        console.error('❌ Erro ao carregar partidas reais:', apiError)
        console.log('🔄 Usando dados mockados como fallback')
      }

      setMatches(finalMatches)

      const apostasIniciais = finalMatches.map((match) => ({
        match_id: match.id,
        resultado: null as "casa" | "empate" | "fora" | null,
      }))
      setApostas(apostasIniciais)

      console.log("✅ Dados da aplicação carregados:", {
        banners: adminBanners.length,
        matches: finalMatches.length,
        apostas: apostasIniciais.length,
        dataSource: finalMatches.length > 0 ? 'real' : 'vazio'
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (banners.length > 0) {
      const timer = setInterval(() => {
        setCurrentSlide((prev) => (prev + 1) % banners.length)
      }, 5000)
      return () => clearInterval(timer)
    }
  }, [banners.length])

  const handleApostaChange = (matchId: number, resultado: "casa" | "empate" | "fora") => {
    console.log("🎯 Aposta alterada:", { matchId, resultado })

    setApostas((prev) => {
      const existingIndex = prev.findIndex((a) => a.match_id === matchId)
      if (existingIndex >= 0) {
        const newApostas = [...prev]
        newApostas[existingIndex] = { match_id: matchId, resultado }
        console.log("✏️ Aposta atualizada:", newApostas[existingIndex])
        return newApostas
      } else {
        const newAposta = { match_id: matchId, resultado }
        console.log("➕ Nova aposta adicionada:", newAposta)
        return [...prev, newAposta]
      }
    })
  }

  const getApostaAtual = (matchId: number) => {
    return apostas.find((a) => a.match_id === matchId)?.resultado || null
  }

  const getApostasFeitas = () => {
    return apostas.filter((a) => a.resultado !== null).length
  }

  const podeFinalizarAposta = () => {
    return getApostasFeitas() >= 11 && !apostasEncerradas
  }

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % banners.length)
  }

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + banners.length) % banners.length)
  }

  const getMatchesByCompetition = (competitionId: string) => {
    return matches.filter((match) => match.competition === competitionId)
  }

  const getCompetitionsWithMatches = () => {
    const competitionsMap = new Map()

    matches.forEach((match) => {
      if (!competitionsMap.has(match.competition)) {
        competitionsMap.set(match.competition, {
          id: match.competition,
          name: match.competitionName || match.competition.replace(/-/g, ' ').toUpperCase(),
          matches: []
        })
      }
      competitionsMap.get(match.competition).matches.push(match)
    })

    return Array.from(competitionsMap.values()).filter(comp => comp.matches.length > 0)
  }

  const formatDateTime = (utcDate: string) => {
    const date = new Date(utcDate)
    const day = date.getDate().toString().padStart(2, '0')
    const month = date.toLocaleDateString("pt-BR", { month: "short" }).replace('.', '')
    const time = date.toLocaleTimeString("pt-BR", { hour: "2-digit", minute: "2-digit" })

    return {
      date: `${day} de ${month}`,
      time: time,
    }
  }

  const handleCPFChange = (value: string) => {
    const formatted = formatCPF(value)
    setRegisterForm({ ...registerForm, cpf: formatted })

    // Validar CPF em tempo real
    const errorMessage = getCPFErrorMessage(formatted)
    setCpfError(errorMessage || "")
  }

  const handleLogin = async () => {
    if (!loginForm.email || !loginForm.senha) {
      setError("Preencha todos os campos")
      return
    }

    setLoginLoading(true)
    setError("")

    try {
      // Verificar se é admin
      if (loginForm.email === "<EMAIL>" && loginForm.senha === "admin123") {
        const adminUser = {
          id: 1,
          nome: "Administrador",
          email: "<EMAIL>",
          tipo: "admin",
        }
        localStorage.setItem("user", JSON.stringify(adminUser))
        window.location.href = "/admin"
        return
      }

      console.log("📤 Enviando dados de login:", { email: loginForm.email })

      // Chamar API real de login
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: loginForm.email,
          senha: loginForm.senha
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Erro no login')
      }

      if (!result.success) {
        throw new Error(result.message || 'Erro no login')
      }

      console.log("✅ Login realizado com sucesso:", result)

      // Armazenar dados do usuário
      localStorage.setItem("user", JSON.stringify(result.user))
      setUser(result.user)
      await loadUserData(result.user.id)

      setShowLoginDialog(false)
      setLoginForm({ email: "", senha: "" })
      toast.success("Login realizado com sucesso!")
    } catch (err) {
      setError("Erro ao fazer login. Tente novamente.")
      console.error("❌ Erro no login:", err)
    } finally {
      setLoginLoading(false)
    }
  }

  const handleRegister = async () => {
    if (!registerForm.nome || !registerForm.email || !registerForm.telefone || !registerForm.senha) {
      setError("Preencha todos os campos")
      return
    }

    // Validar CPF
    const cpfErrorMessage = getCPFErrorMessage(registerForm.cpf)
    if (cpfErrorMessage) {
      setError(cpfErrorMessage)
      setCpfError(cpfErrorMessage)
      return
    }

    if (registerForm.senha.length < 6) {
      setError("Senha deve ter pelo menos 6 caracteres")
      return
    }

    if (registerForm.senha !== registerForm.confirmarSenha) {
      setError("Senhas não coincidem")
      return
    }

    setRegisterLoading(true)
    setError("")

    try {
      // Preparar dados para API
      const affiliateRef = localStorage.getItem('affiliate_ref') || codigoAfiliado

      const registrationData = {
        nome: registerForm.nome,
        email: registerForm.email,
        telefone: registerForm.telefone,
        cpf: registerForm.cpf,
        senha: registerForm.senha,
        affiliate_code: affiliateRef
      }

      console.log("📤 Enviando dados de registro:", registrationData)

      // Chamar API real de registro
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(registrationData)
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao registrar usuário')
      }

      if (!result.success) {
        throw new Error(result.message || 'Erro ao registrar usuário')
      }

      console.log("✅ Usuário registrado com sucesso:", result)

      // Armazenar dados do usuário
      localStorage.setItem("user", JSON.stringify(result.user))
      setUser(result.user)
      await loadUserData(result.user.id)

      // Limpar código de afiliado
      if (affiliateRef) {
        localStorage.removeItem('affiliate_ref')
        setCodigoAfiliado(null)
      }

      // Mensagem de sucesso
      if (result.is_affiliate_referral) {
        toast.success("Conta criada com sucesso! Você foi indicado por um afiliado.")
        // Redirecionar para dashboard após 2 segundos
        setTimeout(() => {
          window.location.href = "/dashboard"
        }, 2000)
      } else {
        toast.success("Conta criada com sucesso!")
      }

      setShowRegisterDialog(false)
      setRegisterForm({
        nome: "",
        email: "",
        telefone: "",
        cpf: "",
        senha: "",
        confirmarSenha: "",
      })
      setCpfError("")
    } catch (err) {
      setError("Erro ao criar conta. Tente novamente.")
      console.error("❌ Erro no registro:", err)
    } finally {
      setRegisterLoading(false)
    }
  }

  const handleLogout = () => {
    try {
      localStorage.removeItem("user")
      setUser(null)
      setUserBilhetes([])
      setUserPagamentos([])
      // Resetar apostas
      setApostas(apostas.map((a) => ({ ...a, resultado: null })))
      toast.success("Logout realizado com sucesso!")
      console.log("✅ Logout realizado")
    } catch (error) {
      console.error("❌ Erro no logout:", error)
    }
  }

  const handleFinalizarAposta = () => {
    if (!podeFinalizarAposta()) {
      setError("Selecione pelo menos 11 partidas para finalizar a aposta")
      toast.error("Selecione pelo menos 11 partidas")
      return
    }

    if (!user) {
      setShowLoginDialog(true)
      return
    }

    setShowPaymentDialog(true)
    generatePixQRCode()
  }

  const generatePixQRCode = async () => {
    setPaymentLoading(true)
    setError("")

    try {
      // Verificar se o usuário está logado
      if (!user || !user.id) {
        setError("Você precisa estar logado para fazer apostas")
        toast.error("Faça login para continuar")
        setShowLoginDialog(true)
        return
      }



      // Validar dados obrigatórios do usuário
      if (!user.nome || !user.email) {
        setError("Dados do usuário incompletos. Faça login novamente.")
        toast.error("Dados do usuário incompletos")
        setShowLoginDialog(true)
        return
      }

      // Garantir que o CPF existe e não está vazio
      const userCpf = user.cpf && user.cpf.trim() !== "" ? user.cpf : "00000000000"

      console.log("🔄 Gerando QR Code PIX...")
      console.log("👤 Dados do usuário para PIX:", {
        nome: user.nome,
        email: user.email,
        cpf: userCpf,
        cpf_original: user.cpf
      })

      // Dados do usuário para o PIX
      const pixRequest = {
        value: valorAposta, // Valor fixo da aposta do bolão
        description: `Bolão Brasil - ${getApostasFeitas()} apostas`,
        client_name: user.nome,
        client_email: user.email,
        client_document: userCpf,
        qrcode_image: true
      }

      console.log("📤 Enviando dados PIX:", pixRequest)

      // Fazer requisição para a API PIX real
      const response = await fetch('/api/pix/qrcode', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(pixRequest)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Erro ao gerar QR Code PIX')
      }

      const pixResponse = await response.json()

      if (!pixResponse.success) {
        throw new Error(pixResponse.error || 'Erro na resposta da API PIX')
      }

      console.log("✅ QR Code PIX gerado com sucesso:", pixResponse.data)

      setPixData({
        qr_code_value: pixResponse.data?.qr_code_value || "",
        qrcode_image: pixResponse.data?.qrcode_image || "",
        expiration_datetime: pixResponse.data?.expiration_datetime || new Date(Date.now() + 30 * 60 * 1000).toISOString(),
        status: pixResponse.data?.status || "pending",
        transaction_id: pixResponse.data?.transaction_id || `BLT${Date.now()}${Math.random().toString(36).substring(2, 11).toUpperCase()}`,
        order_id: pixResponse.data?.order_id || pixResponse.data?.transaction_id || ""
      })

      // Salvar bilhete no banco de dados imediatamente
      const apostasFeitas = apostas.filter((a) => a.resultado !== null)

      // Enriquecer apostas com dados dos jogos
      const apostasEnriquecidas = apostasFeitas.map(aposta => {
        const jogo = matches.find(m => m.id === aposta.match_id)
        return {
          jogo_id: aposta.match_id,
          palpite: aposta.resultado,
          jogo_nome: jogo ? `${jogo.homeTeam.name} vs ${jogo.awayTeam.name}` : `Jogo ${aposta.match_id}`,
          odd: 1.5
        }
      })

      // Garantir que os dados do cliente não estejam vazios
      const clientName = user?.nome?.trim() || "Usuário Padrão"
      const clientEmail = user?.email?.trim() || "<EMAIL>"
      const clientDocument = user?.cpf?.trim() || "00000000000"

      const bilheteData = {
        user_id: user?.id,
        apostas: apostasEnriquecidas,
        valor: valorAposta, // Valor fixo da aposta do bolão
        qr_code_pix: pixResponse.data?.qr_code_value || "",
        transaction_id: pixResponse.data?.transaction_id || "",
        client_name: clientName,
        client_email: clientEmail,
        client_document: clientDocument
      }

      console.log("💾 Salvando bilhete no banco:", bilheteData)

      const saveBilheteResponse = await fetch('/api/bilhetes/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(bilheteData)
      })

      if (!saveBilheteResponse.ok) {
        const errorData = await saveBilheteResponse.json()
        console.error("❌ Erro ao salvar bilhete:", errorData)
        throw new Error(errorData.error || 'Erro ao salvar bilhete')
      }

      const bilheteResult = await saveBilheteResponse.json()
      console.log("✅ Bilhete salvo no banco:", bilheteResult)

      // Criar objeto do bilhete para o frontend
      const novoBilhete: BilheteAposta = {
        id: bilheteResult.bilhete.codigo,
        data: new Date().toLocaleDateString("pt-BR"),
        hora: new Date().toLocaleTimeString("pt-BR", { hour: "2-digit", minute: "2-digit" }),
        apostas: apostas
          .filter((a) => a.resultado !== null)
          .map((a) => {
            const match = matches.find((m) => m.id === a.match_id)
            return {
              jogo: `${match?.homeTeam.shortName} x ${match?.awayTeam.shortName}`,
              resultado: a.resultado === "casa" ? "Casa" : a.resultado === "empate" ? "Empate" : "Fora",
            }
          }),
        valor: bilheteResult.bilhete.valor,
        status: bilheteResult.bilhete.status,
        qr_code_pix: bilheteResult.bilhete.qr_code_pix,
        transaction_id: bilheteResult.bilhete.transaction_id,
      }

      setBilhete(novoBilhete)
      setUserBilhetes((prev) => [novoBilhete, ...prev])

      toast.success("Bilhete criado! Aguardando pagamento PIX.")
      console.log("✅ Bilhete criado:", novoBilhete.id)

      // Iniciar verificação de status do pagamento
      if (bilheteResult.bilhete.transaction_id) {
        startPaymentStatusCheck(bilheteResult.bilhete.transaction_id)
      }
    } catch (error) {
      console.error("❌ Erro ao gerar PIX:", error)
      setError("Erro ao gerar pagamento PIX. Tente novamente.")
      toast.error("Erro ao gerar pagamento PIX")
    } finally {
      setPaymentLoading(false)
    }
  }

  const startPaymentStatusCheck = (transactionId: string) => {
    if (!transactionId) return

    console.log("🔄 Iniciando verificação de status do pagamento:", transactionId)

    const checkInterval = setInterval(async () => {
      try {
        // Consultar status do bilhete no banco local
        const response = await fetch(`/api/user/bilhetes?user_id=${user?.id}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          }
        })

        if (response.ok) {
          const bilhetesData = await response.json()
          console.log("📊 Bilhetes do usuário:", bilhetesData)

          // Procurar bilhete com este transaction_id
          const bilheteAtual = bilhetesData.bilhetes?.find((b: any) => {
            return b.transaction_id === transactionId || b.codigo === transactionId
          })

          console.log("🔍 Bilhete encontrado:", bilheteAtual)

          // Verificar se pagamento foi aprovado
          if (bilheteAtual && (bilheteAtual.status === 'pago' || bilheteAtual.status === 'PAID' || bilheteAtual.status === 'aprovado')) {
            clearInterval(checkInterval)

            // Atualizar status do bilhete para "pago" (que será exibido como "Aprovado")
            setUserBilhetes(prev =>
              prev.map((b: any) =>
                (b.transaction_id === transactionId || b.codigo === transactionId)
                  ? { ...b, status: "pago" }
                  : b
              )
            )

            // Atualizar pixData para mostrar como confirmado
            setPixData(prev => prev ? { ...prev, status: "confirmed" } : null)

            // Notificação de sucesso mais visível
            toast.success("🎉 PAGO COM SUCESSO! Bilhete aprovado.", {
              duration: 8000,
              style: {
                background: '#10B981',
                color: 'white',
                fontSize: '16px',
                fontWeight: 'bold',
                padding: '16px',
                borderRadius: '12px',
                boxShadow: '0 10px 25px rgba(16, 185, 129, 0.3)'
              }
            })

            // Tocar som de sucesso
            playSuccessSound()

            // Mostrar modal de sucesso
            setPaymentSuccessData({
              codigo: bilheteAtual.codigo,
              valor: bilheteAtual.valor_total || bilheteAtual.valor || "0,00",
              data: new Date().toLocaleString('pt-BR'),
              transactionId: transactionId
            })
            setShowPaymentSuccess(true)

            // Fechar modal de pagamento PIX se estiver aberto
            setShowPaymentDialog(false)

            console.log("✅ Pagamento confirmado para:", transactionId)
          }
        }
      } catch (error) {
        console.error("❌ Erro ao verificar status:", error)
      }
    }, 5000) // Verificar a cada 5 segundos

    // Parar verificação após 10 minutos
    setTimeout(() => {
      clearInterval(checkInterval)
      console.log("⏰ Timeout na verificação de pagamento:", transactionId)
    }, 600000)
  }

  const copyPixCode = async () => {
    if (pixData?.qr_code_value) {
      try {
        await navigator.clipboard.writeText(pixData.qr_code_value)
        toast.success("Código PIX copiado!")
        console.log("✅ Código PIX copiado")
      } catch (error) {
        console.error("❌ Erro ao copiar:", error)
        // Fallback para navegadores mais antigos
        try {
          const textArea = document.createElement("textarea")
          textArea.value = pixData.qr_code_value
          textArea.style.position = "fixed"
          textArea.style.left = "-999999px"
          textArea.style.top = "-999999px"
          document.body.appendChild(textArea)
          textArea.focus()
          textArea.select()
          const successful = document.execCommand("copy")
          document.body.removeChild(textArea)

          if (successful) {
            toast.success("Código PIX copiado!")
          } else {
            toast.error("Erro ao copiar código PIX")
          }
        } catch (fallbackError) {
          console.error("❌ Erro no fallback:", fallbackError)
          toast.error("Não foi possível copiar automaticamente. Copie manualmente.")
        }
      }
    }
  }

  const printBilhete = async () => {
    if (!bilhete) return

    setPrintingBilhete(true)

    try {
      console.log("🖨️ Imprimindo bilhete:", bilhete.id)
      await new Promise((resolve) => setTimeout(resolve, 3000))

      // Criar conteúdo do bilhete para impressão (SEM PRÊMIO POTENCIAL)
      const bilheteContent = `
================================
        BOLÃO BRASIL
================================
Bilhete: ${bilhete.id}
Data: ${bilhete.data} ${bilhete.hora}
--------------------------------
SUAS APOSTAS:
${bilhete.apostas
  .map(
    (a, i) => `${i + 1}. ${a.jogo}
   Resultado: ${a.resultado}`,
  )
  .join("\n")}
--------------------------------
Valor: R$ ${bilhete.valor.toFixed(2)}
--------------------------------
${bilhete.qr_code_pix ? `QR Code PIX:\n${bilhete.qr_code_pix}\n--------------------------------\n` : ""}${bilhete.transaction_id ? `ID Transacao: ${bilhete.transaction_id}\n--------------------------------\n` : ""}Boa sorte!
================================
      `

      console.log("📄 Conteúdo do bilhete:", bilheteContent)

      // Criar download do bilhete
      const blob = new Blob([bilheteContent], { type: "text/plain" })
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = `bilhete_${bilhete.id}.txt`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      toast.success("Bilhete impresso e baixado com sucesso!")

      // Resetar apostas após impressão
      setApostas(apostas.map((a) => ({ ...a, resultado: null })))
      setShowBilheteDialog(false)
      console.log("✅ Bilhete impresso e apostas resetadas")
    } catch (error) {
      console.error("❌ Erro ao imprimir bilhete:", error)
      setError("Erro ao imprimir bilhete")
      toast.error("Erro ao imprimir bilhete")
    } finally {
      setPrintingBilhete(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pendente":
        return <Badge variant="secondary">Pendente</Badge>
      case "pago":
      case "aprovado":
        return <Badge className="bg-green-600">Aprovado</Badge>
      case "ganhou":
        return <Badge className="bg-green-600">Ganhou</Badge>
      case "perdeu":
        return <Badge variant="destructive">Perdeu</Badge>
      case "rejeitado":
      case "cancelado":
        return <Badge variant="destructive">Rejeitado</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const handleBilheteClick = (bilheteClicado: BilheteAposta) => {
    console.log("🔍 Abrindo detalhes do bilhete:", bilheteClicado.id)
    setSelectedBilhete(bilheteClicado)
    setShowBilheteDialog(true)
  }

  const handleOpenMeusBilhetes = () => {
    console.log("📋 Abrindo Meus Bilhetes")
    setShowUserBilhetes(true)
  }

  const handleOpenMeusPagamentos = () => {
    console.log("💳 Abrindo Meus Pagamentos")
    setShowUserPagamentos(true)
  }

  const handleOpenMeuPerfil = () => {
    console.log("👤 Abrindo Meu Perfil")
    setShowUserProfile(true)
  }

  const handleOpenMeuAfiliado = () => {
    console.log("🔗 Abrindo Meu Afiliado")
    window.open("/dashboard/afiliado", "_blank")
  }



  // Verificar se CPF é válido para mostrar ícone
  const isCPFValid = registerForm.cpf && validateCPF(registerForm.cpf)
  const showCPFValidation = registerForm.cpf.length >= 11

  if (loading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-green-500 mx-auto mb-4" />
          <p className="text-white">Carregando jogos...</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <style jsx>{`
        .banner-container {
          aspect-ratio: 16/9;
          min-height: 200px;
          max-height: 400px;
        }

        @media (max-width: 640px) {
          .banner-container {
            aspect-ratio: 16/10;
            min-height: 180px;
            max-height: 280px;
          }
        }

        @media (min-width: 641px) and (max-width: 1024px) {
          .banner-container {
            aspect-ratio: 20/9;
            min-height: 220px;
            max-height: 320px;
          }
        }

        @media (min-width: 1025px) {
          .banner-container {
            aspect-ratio: 24/9;
            min-height: 250px;
            max-height: 400px;
          }
        }

        .banner-image {
          image-rendering: -webkit-optimize-contrast;
          image-rendering: crisp-edges;
          object-fit: cover;
          object-position: center;
          width: 100%;
          height: 100%;
        }
      `}</style>

      <div className="min-h-screen bg-slate-900 text-white overflow-x-hidden">
      {/* Banner de Afiliado */}
      {codigoAfiliado && !user && (
        <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white p-3 text-center">
          <div className="flex items-center justify-center gap-2">
            <UserPlus className="h-5 w-5" />
            <span className="font-medium">
              🎉 Você foi convidado por um afiliado! Registre-se e ganhe benefícios especiais!
            </span>
          </div>
        </div>
      )}

      {/* Header */}
      <header className="bg-slate-800 border-b border-slate-700 p-3 sm:p-4 md:p-5 lg:p-6 safe-area-top">
        <div className="w-full max-w-none mx-auto px-4 flex items-center justify-between">
          <h1 className="text-lg sm:text-2xl md:text-3xl lg:text-4xl font-bold text-green-400 truncate">Sistema Bolão</h1>
          <div className="flex items-center gap-2 sm:gap-4 md:gap-6">
            {user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button className="bg-green-600 hover:bg-green-700 text-white px-3 sm:px-4 md:px-6 py-2 md:py-3 rounded-lg font-medium transition-colors flex items-center gap-1 sm:gap-2 text-sm sm:text-base md:text-lg mobile-friendly touch-button">
                    <User className="h-4 w-4 md:h-5 md:w-5" />
                    <span className="hidden sm:inline truncate max-w-[120px] md:max-w-[200px]">{user.nome}</span>
                    <span className="sm:hidden truncate max-w-[80px]">{user.nome.split(' ')[0]}</span>
                    <ChevronDown className="h-4 w-4 md:h-5 md:w-5" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56 bg-slate-800 border-slate-700">
                  <DropdownMenuLabel className="text-white">Minha Conta</DropdownMenuLabel>
                  <DropdownMenuSeparator className="bg-slate-700" />
                  <DropdownMenuItem
                    onClick={handleOpenMeusBilhetes}
                    className="text-slate-300 hover:bg-slate-700 hover:text-white cursor-pointer"
                  >
                    <Receipt className="mr-2 h-4 w-4" />
                    Meus Bilhetes
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleOpenMeusPagamentos}
                    className="text-slate-300 hover:bg-slate-700 hover:text-white cursor-pointer"
                  >
                    <CreditCard className="mr-2 h-4 w-4" />
                    Meus Pagamentos
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleOpenMeuAfiliado}
                    className="text-slate-300 hover:bg-slate-700 hover:text-white cursor-pointer"
                  >
                    <UserPlus className="mr-2 h-4 w-4" />
                    Meu Afiliado
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleOpenMeuPerfil}
                    className="text-slate-300 hover:bg-slate-700 hover:text-white cursor-pointer"
                  >
                    <User className="mr-2 h-4 w-4" />
                    Meu Perfil
                  </DropdownMenuItem>
                  <DropdownMenuSeparator className="bg-slate-700" />
                  <DropdownMenuItem
                    onClick={handleLogout}
                    className="text-red-400 hover:bg-slate-700 hover:text-red-300 cursor-pointer"
                  >
                    <LogOut className="mr-2 h-4 w-4" />
                    Sair
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <Button
                onClick={() => setShowLoginDialog(true)}
                className="bg-green-600 hover:bg-green-700 text-white px-3 sm:px-4 md:px-6 py-2 md:py-3 rounded-lg font-medium transition-colors text-sm sm:text-base md:text-lg mobile-friendly touch-button"
              >
                <span className="hidden sm:inline">Entrar</span>
                <span className="sm:hidden">Login</span>
              </Button>
            )}
          </div>
        </div>
      </header>

      {/* Banner Carousel ou Ranking */}
      {bolaoFinalizado && ranking.length > 0 ? (
        /* Ranking de Apostadores */
        <div className="bg-gradient-to-r from-green-600 to-blue-600 p-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl sm:text-3xl font-bold text-white text-center mb-6">
              🏆 Ranking Final - {bolaoAtual?.nome}
            </h2>
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Posição
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Apostador
                      </th>
                      <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Acertos
                      </th>
                      <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        %
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {ranking.map((apostador, index) => (
                      <tr
                        key={apostador.id}
                        className={`hover:bg-gray-50 cursor-pointer ${
                          index === 0 ? 'bg-yellow-50' :
                          index === 1 ? 'bg-gray-50' :
                          index === 2 ? 'bg-orange-50' : ''
                        }`}
                        onClick={() => {
                          window.open(`/bolao/${bolaoAtual?.id}/apostas/${apostador.id}`, '_blank')
                        }}
                      >
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {index === 0 && <span className="text-2xl mr-2">🥇</span>}
                            {index === 1 && <span className="text-2xl mr-2">🥈</span>}
                            {index === 2 && <span className="text-2xl mr-2">🥉</span>}
                            <span className="text-sm font-medium text-gray-900">
                              {index + 1}º
                            </span>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {apostador.nome}
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-center">
                          <span className="text-sm font-bold text-green-600">
                            {apostador.acertos}/{apostador.total_apostas}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-center">
                          <span className="text-sm text-gray-900">
                            {apostador.percentual_acertos}%
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
            <p className="text-center text-white text-sm mt-4 opacity-90">
              Clique em um apostador para ver suas apostas
            </p>
          </div>
        </div>
      ) : banners.length > 0 ? (
        /* Banner Carousel Sempre Responsivo */
        <div className="banner-container relative w-full aspect-[16/9] sm:aspect-[21/9] md:aspect-[24/9] lg:aspect-[28/9] overflow-hidden shadow-lg">
          <div
            className="flex transition-transform duration-700 ease-in-out h-full"
            style={{ transform: `translateX(-${currentSlide * 100}%)` }}
          >
            {banners.map((banner) => (
              <div
                key={banner.id}
                className={`min-w-full h-full bg-gradient-to-r ${banner.color} flex items-center justify-center relative overflow-hidden`}
              >
                {/* Imagem de fundo otimizada */}
                {banner.image && banner.image !== "/placeholder.svg?height=200&width=400" && (
                  <div className="absolute inset-0">
                    <img
                      src={banner.image}
                      alt={banner.title}
                      className="banner-image w-full h-full object-cover object-center"
                      style={{
                        filter: 'contrast(1.1) brightness(0.9) saturate(1.1)',
                        transform: 'translateZ(0)'
                      }}
                      loading="eager"
                      decoding="async"
                    />
                    <div className="absolute inset-0 bg-gradient-to-r from-black/50 via-black/30 to-black/50"></div>
                  </div>
                )}

                {/* Banner sem texto sobreposto */}
              </div>
            ))}
          </div>

          {/* Navigation Arrows melhorados */}
          {banners.length > 1 && (
            <>
              <button
                onClick={prevSlide}
                className="absolute left-2 sm:left-4 top-1/2 transform -translate-y-1/2 bg-black/40 hover:bg-black/70 backdrop-blur-sm rounded-full p-2 sm:p-3 transition-all duration-300 hover:scale-110 border border-white/20"
                aria-label="Banner anterior"
              >
                <ChevronLeft className="h-4 w-4 sm:h-6 sm:w-6 text-white" />
              </button>
              <button
                onClick={nextSlide}
                className="absolute right-2 sm:right-4 top-1/2 transform -translate-y-1/2 bg-black/40 hover:bg-black/70 backdrop-blur-sm rounded-full p-2 sm:p-3 transition-all duration-300 hover:scale-110 border border-white/20"
                aria-label="Próximo banner"
              >
                <ChevronRight className="h-4 w-4 sm:h-6 sm:w-6 text-white" />
              </button>
            </>
          )}

          {/* Dots Indicator melhorados */}
          {banners.length > 1 && (
            <div className="absolute bottom-3 sm:bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 sm:space-x-3">
              {banners.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentSlide(index)}
                  className={`w-2 h-2 sm:w-3 sm:h-3 rounded-full transition-all duration-300 border border-white/30 ${
                    index === currentSlide
                      ? "bg-white scale-125 shadow-lg"
                      : "bg-white/50 hover:bg-white/70 hover:scale-110"
                  }`}
                  aria-label={`Ir para banner ${index + 1}`}
                />
              ))}
            </div>
          )}

          {/* Indicador de progresso */}
          {banners.length > 1 && (
            <div className="absolute bottom-0 left-0 w-full h-1 bg-black/20">
              <div
                className="h-full bg-gradient-to-r from-yellow-400 to-orange-500 transition-all duration-700 ease-in-out"
                style={{ width: `${((currentSlide + 1) / banners.length) * 100}%` }}
              ></div>
            </div>
          )}
        </div>
      ) : null}

      {/* Main Content */}
      <main className="p-2 sm:p-4 md:p-6 lg:p-8 w-full max-w-none mx-auto">
        {getCompetitionsWithMatches().map((competition) => {
          return (
            <div key={competition.id} className="mb-8 w-full">
              {/* Competition Header */}
              <div className="bg-green-600 text-white p-3 sm:p-4 md:p-5 lg:p-6 rounded-t-lg">
                <div className="flex items-center justify-center gap-3 md:gap-4">
                  <CompetitionEmblem
                    competitionCode={(() => {
                      const id = competition.id.toLowerCase()
                      const name = competition.name.toLowerCase()

                      // Serie A italiana (verificar primeiro)
                      if (name.includes('serie a') && !name.includes('brasil')) return 'SA'

                      // Brasileirão
                      if (id.includes('brasileirao') || name.includes('brasileir')) return 'BSA'

                      // Outras competições
                      if (id.includes('premier')) return 'PL'
                      if (id.includes('la-liga') || id.includes('primera')) return 'PD'
                      if (id.includes('bundesliga')) return 'BL1'
                      if (id.includes('ligue')) return 'FL1'
                      if (id.includes('champions')) return 'CL'
                      if (id.includes('europa')) return 'EL'

                      return competition.id.toUpperCase()
                    })()}
                    alt={competition.name}
                    size="lg"
                  />
                  <h2 className="text-lg sm:text-xl font-bold">{competition.name}</h2>
                </div>
              </div>

              {/* Competition Subheader - Desktop & Tablet */}
              <div
                className="hidden sm:flex text-white p-3 md:p-4 justify-end items-center text-sm md:text-base font-medium"
                style={{ backgroundColor: "rgb(30, 64, 175)" }}
              >
                <div className="flex gap-6 md:gap-8 lg:gap-12">
                  <span className="min-w-[60px] text-center">Casa</span>
                  <span className="min-w-[60px] text-center">Empate</span>
                  <span className="min-w-[60px] text-center">Fora</span>
                </div>
              </div>

              {/* Competition Subheader - Mobile */}
              <div
                className="block sm:hidden text-white p-2 text-center text-xs font-medium"
                style={{ backgroundColor: "rgb(30, 64, 175)" }}
              >
                <span>Selecione suas apostas</span>
              </div>

              {/* Matches */}
              <div className="bg-slate-800 rounded-b-lg overflow-hidden">
                {competition.matches.map((match: any, index: number) => {
                  const { date, time } = formatDateTime(match.utcDate)
                  const apostaAtual = getApostaAtual(match.id)

                  return (
                    <div
                      key={match.id}
                      className={`p-4 sm:p-5 md:p-6 border-b border-slate-700 ${
                        index === competition.matches.length - 1 ? "border-b-0" : ""
                      }`}
                    >
                      {/* Layout Desktop & Tablet */}
                      <div className="hidden sm:flex items-center justify-between">
                        <div className="flex items-center gap-3 md:gap-4 flex-1">
                          <div className="flex items-center gap-2 md:gap-3">
                            <TeamLogo
                              src={match.homeTeam.crest}
                              alt={match.homeTeam.name}
                              size="sm"
                            />
                            <span className="font-medium text-white text-sm md:text-base lg:text-lg">{match.homeTeam.name}</span>
                          </div>
                          <span className="text-slate-400 font-bold mx-2 md:mx-3 text-sm md:text-base">VS</span>
                          <div className="flex items-center gap-2 md:gap-3">
                            <TeamLogo
                              src={match.awayTeam.crest}
                              alt={match.awayTeam.name}
                              size="sm"
                            />
                            <span className="font-medium text-white text-sm md:text-base lg:text-lg">{match.awayTeam.name}</span>
                          </div>
                        </div>

                        <div className="text-sm md:text-base text-slate-400 mx-4 md:mx-6 font-medium text-center">
                          {date} - {time}
                        </div>

                        <div className="hidden sm:flex gap-2 md:gap-3">
                          <Button
                            size="sm"
                            variant={apostaAtual === "casa" ? "default" : "outline"}
                            className={`w-20 md:w-24 lg:w-28 h-12 md:h-14 font-bold text-sm md:text-base transition-all duration-200 mobile-friendly ${
                              apostaAtual === "casa"
                                ? "bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg border-2 border-orange-400 transform scale-105"
                                : "border-2 border-slate-500 bg-slate-700 text-slate-200 hover:bg-slate-600 hover:border-slate-400 hover:text-white shadow-md"
                            }`}
                            onClick={() => handleApostaChange(match.id, "casa")}
                          >
                            <div className="flex flex-col items-center gap-1">
                              <div className="w-10 h-10 flex items-center justify-center">
                                {match.homeTeam.crest ? (
                                  <img
                                    src={match.homeTeam.crest}
                                    alt={match.homeTeam.name}
                                    className="w-10 h-10 object-contain"
                                    onError={(e) => {
                                      const target = e.currentTarget as HTMLImageElement
                                      target.style.display = 'none'
                                      const fallback = target.nextElementSibling as HTMLElement
                                      if (fallback) fallback.style.display = 'flex'
                                    }}
                                  />
                                ) : null}
                                <div
                                  className="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-base font-bold text-white shadow-sm border border-blue-300"
                                  style={{ display: match.homeTeam.crest ? 'none' : 'flex' }}
                                >
                                  {match.homeTeam.shortName.substring(0, 2).toUpperCase()}
                                </div>
                              </div>
                            </div>
                          </Button>
                          <Button
                            size="sm"
                            variant={apostaAtual === "empate" ? "default" : "outline"}
                            className={`w-16 h-12 font-bold text-sm transition-all duration-200 ${
                              apostaAtual === "empate"
                                ? "bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg border-2 border-orange-400 transform scale-105"
                                : "border-2 border-slate-500 bg-slate-700 text-slate-200 hover:bg-slate-600 hover:border-slate-400 hover:text-white shadow-md"
                            }`}
                            onClick={() => handleApostaChange(match.id, "empate")}
                          >
                            <div className="flex flex-col items-center">
                              <span className="text-lg font-black">=</span>
                              <span className="text-xs">Empate</span>
                            </div>
                          </Button>
                          <Button
                            size="sm"
                            variant={apostaAtual === "fora" ? "default" : "outline"}
                            className={`w-20 md:w-24 lg:w-28 h-12 md:h-14 font-bold text-sm transition-all duration-200 mobile-friendly ${
                              apostaAtual === "fora"
                                ? "bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg border-2 border-orange-400 transform scale-105"
                                : "border-2 border-slate-500 bg-slate-700 text-slate-200 hover:bg-slate-600 hover:border-slate-400 hover:text-white shadow-md"
                            }`}
                            onClick={() => handleApostaChange(match.id, "fora")}
                          >
                            <div className="flex flex-col items-center gap-1">
                              <div className="w-10 h-10 flex items-center justify-center">
                                {match.awayTeam.crest ? (
                                  <img
                                    src={match.awayTeam.crest}
                                    alt={match.awayTeam.name}
                                    className="w-10 h-10 object-contain"
                                    onError={(e) => {
                                      const target = e.currentTarget as HTMLImageElement
                                      target.style.display = 'none'
                                      const fallback = target.nextElementSibling as HTMLElement
                                      if (fallback) fallback.style.display = 'flex'
                                    }}
                                  />
                                ) : null}
                                <div
                                  className="w-10 h-10 bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center text-base font-bold text-white shadow-sm border border-red-300"
                                  style={{ display: match.awayTeam.crest ? 'none' : 'flex' }}
                                >
                                  {match.awayTeam.shortName.substring(0, 2).toUpperCase()}
                                </div>
                              </div>
                            </div>
                          </Button>
                        </div>
                      </div>

                      {/* Layout Mobile */}
                      <div className="block sm:hidden">
                        {/* Times e VS - Layout Horizontal Compacto para Mobile */}
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-2 flex-1 min-w-0">
                            <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
                              {match.homeTeam.crest ? (
                                <img
                                  src={match.homeTeam.crest}
                                  alt={match.homeTeam.name}
                                  className="w-6 h-6 object-contain"
                                  onError={(e) => {
                                    const target = e.currentTarget as HTMLImageElement
                                    target.style.display = 'none'
                                    const fallback = target.nextElementSibling as HTMLElement
                                    if (fallback) fallback.style.display = 'flex'
                                  }}
                                />
                              ) : null}
                              <div
                                className="w-6 h-6 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-sm border border-blue-300"
                                style={{ display: match.homeTeam.crest ? 'none' : 'flex' }}
                              >
                                {match.homeTeam.shortName.substring(0, 1).toUpperCase()}
                              </div>
                            </div>
                            <span className="font-medium text-white text-sm truncate">{match.homeTeam.name}</span>
                          </div>

                          <span className="text-slate-400 font-bold mx-3 text-xs">VS</span>

                          <div className="flex items-center gap-2 flex-1 min-w-0 justify-end">
                            <span className="font-medium text-white text-sm truncate">{match.awayTeam.name}</span>
                            <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
                              {match.awayTeam.crest ? (
                                <img
                                  src={match.awayTeam.crest}
                                  alt={match.awayTeam.name}
                                  className="w-6 h-6 object-contain"
                                  onError={(e) => {
                                    const target = e.currentTarget as HTMLImageElement
                                    target.style.display = 'none'
                                    const fallback = target.nextElementSibling as HTMLElement
                                    if (fallback) fallback.style.display = 'flex'
                                  }}
                                />
                              ) : null}
                              <div
                                className="w-6 h-6 bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-sm border border-red-300"
                                style={{ display: match.awayTeam.crest ? 'none' : 'flex' }}
                              >
                                {match.awayTeam.shortName.substring(0, 1).toUpperCase()}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Data/Hora */}
                        <div className="text-sm text-slate-400 text-center mb-4 font-medium">
                          {date} - {time}
                        </div>

                        {/* Botões de Aposta Mobile */}
                        <div className="grid grid-cols-3 gap-2">
                          <Button
                            size="sm"
                            variant={apostaAtual === "casa" ? "default" : "outline"}
                            className={`h-16 text-xs font-bold transition-all duration-200 mobile-friendly ${
                              apostaAtual === "casa"
                                ? "bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg border-2 border-orange-400 transform scale-105"
                                : "border-2 border-slate-500 bg-slate-700 text-slate-200 hover:bg-slate-600 hover:border-slate-400 hover:text-white shadow-md"
                            }`}
                            onClick={() => handleApostaChange(match.id, "casa")}
                          >
                            <div className="flex flex-col items-center gap-1">
                              <div className="w-8 h-8 flex items-center justify-center">
                                {match.homeTeam.crest ? (
                                  <img
                                    src={match.homeTeam.crest}
                                    alt={match.homeTeam.name}
                                    className="w-8 h-8 object-contain"
                                    onError={(e) => {
                                      const target = e.currentTarget as HTMLImageElement
                                      target.style.display = 'none'
                                      const fallback = target.nextElementSibling as HTMLElement
                                      if (fallback) fallback.style.display = 'flex'
                                    }}
                                  />
                                ) : null}
                                <div
                                  className="w-8 h-8 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-sm border border-blue-300"
                                  style={{ display: match.homeTeam.crest ? 'none' : 'flex' }}
                                >
                                  {match.homeTeam.shortName.substring(0, 2).toUpperCase()}
                                </div>
                              </div>
                              <span className="text-xs font-medium leading-tight text-center">Casa</span>
                            </div>
                          </Button>

                          <Button
                            size="sm"
                            variant={apostaAtual === "empate" ? "default" : "outline"}
                            className={`h-16 text-xs font-bold transition-all duration-200 mobile-friendly ${
                              apostaAtual === "empate"
                                ? "bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg border-2 border-orange-400 transform scale-105"
                                : "border-2 border-slate-500 bg-slate-700 text-slate-200 hover:bg-slate-600 hover:border-slate-400 hover:text-white shadow-md"
                            }`}
                            onClick={() => handleApostaChange(match.id, "empate")}
                          >
                            <div className="flex flex-col items-center gap-1">
                              <span className="text-xl font-black">=</span>
                              <span className="text-xs font-bold">Empate</span>
                            </div>
                          </Button>

                          <Button
                            size="sm"
                            variant={apostaAtual === "fora" ? "default" : "outline"}
                            className={`h-16 text-xs font-bold transition-all duration-200 mobile-friendly ${
                              apostaAtual === "fora"
                                ? "bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white shadow-lg border-2 border-orange-400 transform scale-105"
                                : "border-2 border-slate-500 bg-slate-700 text-slate-200 hover:bg-slate-600 hover:border-slate-400 hover:text-white shadow-md"
                            }`}
                            onClick={() => handleApostaChange(match.id, "fora")}
                          >
                            <div className="flex flex-col items-center gap-1">
                              <div className="w-8 h-8 flex items-center justify-center">
                                {match.awayTeam.crest ? (
                                  <img
                                    src={match.awayTeam.crest}
                                    alt={match.awayTeam.name}
                                    className="w-8 h-8 object-contain"
                                    onError={(e) => {
                                      const target = e.currentTarget as HTMLImageElement
                                      target.style.display = 'none'
                                      const fallback = target.nextElementSibling as HTMLElement
                                      if (fallback) fallback.style.display = 'flex'
                                    }}
                                  />
                                ) : null}
                                <div
                                  className="w-8 h-8 bg-gradient-to-br from-red-400 to-red-600 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-sm border border-red-300"
                                  style={{ display: match.awayTeam.crest ? 'none' : 'flex' }}
                                >
                                  {match.awayTeam.shortName.substring(0, 2).toUpperCase()}
                                </div>
                              </div>
                              <span className="text-xs font-medium leading-tight text-center">Fora</span>
                            </div>
                          </Button>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          )
        })}

        {/* Betting Summary */}
        <div className="bg-slate-800 rounded-lg p-3 sm:p-4 mt-4 sm:mt-6">
          {apostasEncerradas ? (
            <div className="text-center">
              <div className="bg-red-600/20 border border-red-600/30 rounded-lg p-4 mb-4">
                <h3 className="text-red-400 font-bold text-lg mb-2">⏰ Apostas Encerradas</h3>
                <p className="text-red-300 text-sm">
                  As apostas foram encerradas pois o primeiro jogo já começou.
                </p>
                {!bolaoFinalizado && (
                  <p className="text-yellow-300 text-sm mt-2">
                    Aguarde o final de todos os jogos para ver o ranking final.
                  </p>
                )}
              </div>
            </div>
          ) : (
            <>
              <div className="text-center mb-3 sm:mb-4">
                <p className="text-base sm:text-lg font-medium">{getApostasFeitas()}/11 APOSTAS SELECIONADAS</p>
              </div>
              <Button
                onClick={handleFinalizarAposta}
                disabled={!podeFinalizarAposta()}
                className={`w-full py-3 text-sm sm:text-lg font-medium rounded-lg transition-colors ${
                  podeFinalizarAposta()
                    ? "bg-green-600 hover:bg-green-700 text-white"
                    : "bg-slate-600 text-slate-400 cursor-not-allowed"
                }`}
              >
                {podeFinalizarAposta() ? `FINALIZAR APOSTA - R$ ${valorAposta.toFixed(2)}` : "SELECIONE 11 APOSTAS"}
              </Button>
            </>
          )}
        </div>
      </main>

      {/* Login Dialog */}
      <Dialog open={showLoginDialog} onOpenChange={setShowLoginDialog}>
        <DialogContent className="w-[95vw] max-w-md bg-gradient-to-br from-slate-800 to-slate-900 border-slate-600 shadow-2xl backdrop-blur-sm">
          <DialogHeader>
            <DialogTitle className="text-white text-lg sm:text-xl">Entrar na sua conta</DialogTitle>
            <DialogDescription className="text-slate-400 text-sm sm:text-base">
              Digite suas credenciais para acessar o sistema
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {error && (
              <Alert className="border-red-500 bg-red-500/10">
                <AlertDescription className="text-red-400">{error}</AlertDescription>
              </Alert>
            )}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-white">
                Email
              </Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={loginForm.email}
                  onChange={(e) => setLoginForm({ ...loginForm, email: e.target.value })}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:ring-2 focus:ring-green-500 transition-all duration-200"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="senha" className="text-white">
                Senha
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  id="senha"
                  type={showPassword ? "text" : "password"}
                  placeholder="Sua senha"
                  value={loginForm.senha}
                  onChange={(e) => setLoginForm({ ...loginForm, senha: e.target.value })}
                  className="pl-10 pr-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:ring-2 focus:ring-green-500 transition-all duration-200"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-3 text-slate-400 hover:text-white"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>
            <div className="flex gap-2">
              <Button onClick={handleLogin} disabled={loginLoading} className="flex-1 bg-green-600 hover:bg-green-700">
                {loginLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                Entrar
              </Button>
              <Button
                onClick={() => {
                  setShowLoginDialog(false)
                  setShowRegisterDialog(true)
                  setError("")
                }}
                className="flex-1 bg-green-600 hover:bg-green-700"
              >
                Criar conta
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Register Dialog */}
      <Dialog open={showRegisterDialog} onOpenChange={setShowRegisterDialog}>
        <DialogContent className="w-[95vw] max-w-md bg-gradient-to-br from-slate-800 to-slate-900 border-slate-600 shadow-2xl backdrop-blur-sm max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-white text-lg sm:text-xl">Criar nova conta</DialogTitle>
            <DialogDescription className="text-slate-400">
              {codigoAfiliado ? (
                <div className="bg-green-600/20 border border-green-600/30 rounded-lg p-3 mt-2">
                  <div className="flex items-center gap-2 text-green-400">
                    <UserPlus className="h-4 w-4" />
                    <span className="font-medium">Indicação de Afiliado</span>
                  </div>
                  <p className="text-sm text-green-300 mt-1">
                    Você está se registrando através do link de um afiliado!
                    Ao criar sua conta, você e o afiliado receberão benefícios especiais.
                  </p>
                  <p className="text-xs text-green-400 mt-1">
                    Código: <span className="font-mono">{codigoAfiliado}</span>
                  </p>
                </div>
              ) : (
                "Preencha os dados para criar sua conta"
              )}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {error && (
              <Alert className="border-red-500 bg-red-500/10">
                <AlertDescription className="text-red-400">{error}</AlertDescription>
              </Alert>
            )}
            <div className="space-y-2">
              <Label htmlFor="nome" className="text-white">
                Nome completo
              </Label>
              <div className="relative">
                <User className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  id="nome"
                  type="text"
                  placeholder="Seu nome completo"
                  value={registerForm.nome}
                  onChange={(e) => setRegisterForm({ ...registerForm, nome: e.target.value })}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:ring-2 focus:ring-green-500 transition-all duration-200"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="register-email" className="text-white">
                Email
              </Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  id="register-email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={registerForm.email}
                  onChange={(e) => setRegisterForm({ ...registerForm, email: e.target.value })}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:ring-2 focus:ring-green-500 transition-all duration-200"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="telefone" className="text-white">
                Telefone
              </Label>
              <div className="relative">
                <Phone className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  id="telefone"
                  type="tel"
                  placeholder="(11) 99999-9999"
                  value={registerForm.telefone}
                  onChange={(e) => setRegisterForm({ ...registerForm, telefone: e.target.value })}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:ring-2 focus:ring-green-500 transition-all duration-200"
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="cpf" className="text-white">
                CPF
              </Label>
              <div className="relative">
                <User className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  id="cpf"
                  type="text"
                  placeholder="000.000.000-00"
                  value={registerForm.cpf}
                  onChange={(e) => handleCPFChange(e.target.value)}
                  className={`pl-10 pr-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:ring-2 transition-all duration-200 ${
                    cpfError ? "focus:ring-red-500 border-red-500" : "focus:ring-green-500"
                  }`}
                  maxLength={14}
                />
                {showCPFValidation && (
                  <div className="absolute right-3 top-3">
                    {isCPFValid ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-500" />
                    )}
                  </div>
                )}
              </div>
              {cpfError && <p className="text-red-400 text-sm mt-1">{cpfError}</p>}
            </div>
            <div className="space-y-2">
              <Label htmlFor="register-senha" className="text-white">
                Senha
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  id="register-senha"
                  type={showPassword ? "text" : "password"}
                  placeholder="Mínimo 6 caracteres"
                  value={registerForm.senha}
                  onChange={(e) => setRegisterForm({ ...registerForm, senha: e.target.value })}
                  className="pl-10 pr-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:ring-2 focus:ring-green-500 transition-all duration-200"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-3 text-slate-400 hover:text-white"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmar-senha" className="text-white">
                Confirmar senha
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  id="confirmar-senha"
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="Confirme sua senha"
                  value={registerForm.confirmarSenha}
                  onChange={(e) => setRegisterForm({ ...registerForm, confirmarSenha: e.target.value })}
                  className="pl-10 pr-10 bg-slate-700 border-slate-600 text-white placeholder:text-slate-400 focus:ring-2 focus:ring-green-500 transition-all duration-200"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-3 text-slate-400 hover:text-white"
                >
                  {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                onClick={handleRegister}
                disabled={registerLoading || !!cpfError}
                className="flex-1 bg-green-600 hover:bg-green-700 disabled:opacity-50"
              >
                {registerLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                Criar conta
              </Button>
              <Button
                onClick={() => {
                  setShowRegisterDialog(false)
                  setShowLoginDialog(true)
                  setError("")
                  setCpfError("")
                }}
                className="flex-1 bg-green-600 hover:bg-green-700"
              >
                Já tenho conta
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* User Bilhetes Dialog */}
      <Dialog open={showUserBilhetes} onOpenChange={setShowUserBilhetes}>
        <DialogContent className="w-[95vw] max-w-2xl bg-slate-800 border-slate-700 max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-white flex items-center gap-2">
              <Receipt className="h-5 w-5" />
              Meus Bilhetes
            </DialogTitle>
            <DialogDescription className="text-slate-400">Histórico de todas as suas apostas</DialogDescription>
          </DialogHeader>
          <div className="space-y-3">
            {userBilhetes.length === 0 ? (
              <div className="text-center py-8">
                <Receipt className="h-12 w-12 text-slate-600 mx-auto mb-4" />
                <p className="text-slate-400">Você ainda não fez nenhuma aposta</p>
              </div>
            ) : (
              userBilhetes.map((bilhete) => (
                <div
                  key={bilhete.id}
                  className="bg-slate-700 border border-slate-600 rounded-lg p-4 cursor-pointer hover:bg-slate-600 transition-colors"
                  onClick={() => handleBilheteClick(bilhete)}
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="text-white font-medium">Bilhete #{bilhete.id}</h3>
                      <p className="text-slate-400 text-sm flex items-center gap-1 mt-1">
                        <Calendar className="h-4 w-4" />
                        {bilhete.data} - {bilhete.hora}
                      </p>
                    </div>
                    <div className="text-right">
                      {getStatusBadge(bilhete.status)}
                      <div className="text-white font-medium mt-1 flex items-center gap-1">
                        <DollarSign className="h-4 w-4" />
                        R$ {bilhete.valor.toFixed(2)}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* User Pagamentos Dialog */}
      <Dialog open={showUserPagamentos} onOpenChange={setShowUserPagamentos}>
        <DialogContent className="w-[95vw] max-w-3xl bg-slate-800 border-slate-700 max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-white flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Meus Pagamentos
            </DialogTitle>
            <DialogDescription className="text-slate-400">Histórico de todos os seus pagamentos</DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {userPagamentos.length === 0 ? (
              <div className="text-center py-8">
                <CreditCard className="h-12 w-12 text-slate-600 mx-auto mb-4" />
                <p className="text-slate-400">Você ainda não fez nenhum pagamento</p>
              </div>
            ) : (
              userPagamentos.map((pagamento) => (
                <Card key={pagamento.id} className="bg-slate-700 border-slate-600">
                  <CardContent className="p-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-white font-medium">#{pagamento.id}</span>
                          {getStatusBadge(pagamento.status)}
                        </div>
                        <div className="text-slate-400 text-sm flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          {pagamento.data}
                        </div>
                        <div className="text-slate-400 text-sm mt-1">Bilhete: {pagamento.bilhete_id}</div>
                      </div>
                      <div className="text-right">
                        <div className="text-white font-medium text-lg flex items-center gap-1">
                          <DollarSign className="h-5 w-5" />
                          R$ {pagamento.valor.toFixed(2)}
                        </div>
                        <div className="text-slate-400 text-sm">{pagamento.metodo}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* User Profile Dialog */}
      <Dialog open={showUserProfile} onOpenChange={setShowUserProfile}>
        <DialogContent className="w-[95vw] max-w-4xl bg-slate-800 border-slate-700 max-h-[85vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-white flex items-center gap-2">
              <User className="h-5 w-5" />
              Meu Perfil
            </DialogTitle>
            <DialogDescription className="text-slate-400">
              Informações completas da sua conta e histórico de apostas
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Informações Pessoais */}
            <Card className="bg-slate-700 border-slate-600">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Informações Pessoais
                </CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-slate-400">Nome</Label>
                  <p className="text-white font-medium">{user?.nome}</p>
                </div>
                <div>
                  <Label className="text-slate-400">Email</Label>
                  <p className="text-white font-medium">{user?.email}</p>
                </div>
                {user?.telefone && (
                  <div>
                    <Label className="text-slate-400">Telefone</Label>
                    <p className="text-white font-medium">{user.telefone}</p>
                  </div>
                )}
                {user?.cpf && (
                  <div>
                    <Label className="text-slate-400">CPF</Label>
                    <p className="text-white font-medium">{user.cpf}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Estatísticas Gerais */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card className="bg-gradient-to-br from-blue-600 to-blue-700 border-blue-500">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-white mb-1">{userStats.totalApostas}</div>
                  <div className="text-blue-100 text-sm">Total de Apostas</div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-red-600 to-red-700 border-red-500">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-white mb-1 flex items-center justify-center gap-1">
                    <DollarSign className="h-5 w-5" />
                    {userStats.totalGasto.toFixed(2)}
                  </div>
                  <div className="text-red-100 text-sm">Total Investido</div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-to-br from-green-600 to-green-700 border-green-500">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-white mb-1 flex items-center justify-center gap-1">
                    <DollarSign className="h-5 w-5" />
                    {userStats.totalGanho.toFixed(2)}
                  </div>
                  <div className="text-green-100 text-sm">Total Ganho</div>
                </CardContent>
              </Card>
            </div>

            {/* Ações Rápidas */}
            <div className="flex gap-3">
              <Button
                className="flex-1 bg-green-600 hover:bg-green-700"
                onClick={() => {
                  setShowUserProfile(false)
                  window.scrollTo({ top: document.body.scrollHeight, behavior: "smooth" })
                }}
              >
                <DollarSign className="mr-2 h-4 w-4" />
                Nova Aposta
              </Button>
              <Button
                className="flex-1 bg-green-600 hover:bg-green-700 text-white"
                onClick={() => {
                  setShowUserProfile(false)
                  setShowUserBilhetes(true)
                }}
              >
                <Receipt className="mr-2 h-4 w-4" />
                Ver todos os bilhetes ({userBilhetes.length})
              </Button>
              <Button
                className="flex-1 bg-green-600 hover:bg-green-700 text-white"
                onClick={() => {
                  setShowUserProfile(false)
                  setShowUserPagamentos(true)
                }}
              >
                <CreditCard className="mr-2 h-4 w-4" />
                Ver Pagamentos
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* PIX Payment Dialog */}
      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent className="w-[95vw] max-w-md sm:max-w-lg bg-slate-800 border-slate-700 max-h-[95vh] overflow-y-auto p-4 sm:p-6">
          <DialogHeader className="text-center">
            <DialogTitle className="text-white text-lg sm:text-xl font-bold">💳 Pagamento PIX</DialogTitle>
            <DialogDescription className="text-slate-400 text-sm sm:text-base">
              Escaneie o QR Code para pagar sua aposta
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 sm:space-y-6">
            {paymentLoading ? (
              <div className="flex flex-col items-center justify-center py-8 sm:py-12">
                <Loader2 className="h-10 w-10 sm:h-12 sm:w-12 animate-spin text-green-500 mb-4" />
                <p className="text-white text-base sm:text-lg">Gerando QR Code PIX...</p>
                <p className="text-slate-400 text-sm mt-2">Aguarde um momento...</p>
              </div>
            ) : pixData ? (
              <>
                {/* QR Code */}
                <div className="flex justify-center items-center">
                  <div className="bg-white p-3 sm:p-4 rounded-xl shadow-lg border-2 border-slate-600">
                    {(() => {
                      console.log('🔍 PIX Data Debug COMPLETO:', {
                        pixData: pixData,
                        hasQrcodeImage: !!pixData.qrcode_image,
                        hasQrCodeValue: !!pixData.qr_code_value,
                        qrCodeValueLength: pixData.qr_code_value?.length,
                        qrCodeValueStart: pixData.qr_code_value?.substring(0, 100),
                        qrCodeValueFull: pixData.qr_code_value
                      })
                      return null
                    })()}

                    {pixData.qr_code_value ? (
                      <div className="flex justify-center items-center">
                        <QRCodeComponent
                          value={pixData.qr_code_value}
                          size={window.innerWidth < 640 ? 160 : 200}
                          className="rounded-lg"
                        />
                      </div>
                    ) : pixData.qrcode_image ? (
                      <div className="flex justify-center items-center">
                        <img
                          src={pixData.qrcode_image}
                          alt="QR Code PIX"
                          className="w-40 h-40 sm:w-50 sm:h-50 object-contain rounded-lg"
                          onLoad={() => console.log('✅ Imagem QR Code carregada')}
                          onError={() => console.log('❌ Erro ao carregar imagem QR Code')}
                        />
                      </div>
                    ) : (
                      <div className="w-40 h-40 sm:w-50 sm:h-50 bg-gray-100 flex items-center justify-center rounded-lg">
                        <span className="text-gray-500 text-sm text-center">QR Code<br/>indisponível</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Payment Info */}
                <div className="text-center space-y-2 bg-slate-700 p-4 rounded-lg">
                  <div className="flex justify-between items-center">
                    <span className="text-slate-400 text-sm">Valor:</span>
                    <span className="text-xl sm:text-2xl font-bold text-green-400">R$ {valorAposta.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-400 text-sm">Apostas:</span>
                    <span className="text-white font-medium">{getApostasFeitas()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-slate-400 text-sm">Expira em:</span>
                    <span className="text-white text-sm">
                      {new Date(pixData.expiration_datetime).toLocaleString("pt-BR", {
                        day: '2-digit',
                        month: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </span>
                  </div>
                </div>

                {/* PIX Code */}
                <div className="bg-slate-700 p-3 rounded-lg">
                  <p className="text-slate-400 text-xs mb-2">Código PIX:</p>
                  <p className="text-xs text-slate-300 font-mono break-all leading-relaxed max-h-20 overflow-y-auto bg-slate-800 p-2 rounded border">
                    {pixData.qr_code_value}
                  </p>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <Button
                    onClick={copyPixCode}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 text-base sm:text-lg rounded-lg transition-colors"
                  >
                    📋 Copiar código PIX
                  </Button>

                  {pixData.status === "confirmed" ? (
                    <Button
                      onClick={() => {
                        setShowPaymentDialog(false)
                        setShowBilheteDialog(true)
                      }}
                      className="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-3 text-base sm:text-lg rounded-lg"
                    >
                      🎫 Ver Bilhete e Imprimir
                    </Button>
                  ) : (
                    <div className="bg-yellow-600/20 border border-yellow-600/50 rounded-lg p-3">
                      <div className="flex items-center justify-center space-x-2">
                        <Loader2 className="h-4 w-4 animate-spin text-yellow-400" />
                        <span className="text-yellow-400 text-sm font-medium">
                          Aguardando confirmação do pagamento...
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </>
            ) : null}
          </div>
        </DialogContent>
      </Dialog>

      {/* Bilhete Dialog */}
      <Dialog open={showBilheteDialog} onOpenChange={setShowBilheteDialog}>
        <DialogContent className="w-[95vw] max-w-md bg-slate-800 border-slate-700 max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-white">Bilhete de Aposta</DialogTitle>
            <DialogDescription className="text-slate-400">
              {selectedBilhete ? "Detalhes do bilhete" : "Seu bilhete foi gerado com sucesso!"}
            </DialogDescription>
          </DialogHeader>
          {(bilhete || selectedBilhete) && (
            <div className="space-y-4">
              <div className="bg-slate-700 p-4 rounded-lg space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-slate-400">Bilhete:</span>
                  <span className="text-white font-mono">{(selectedBilhete || bilhete)?.id}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-slate-400">Data/Hora:</span>
                  <span className="text-white">
                    {(selectedBilhete || bilhete)?.data} - {(selectedBilhete || bilhete)?.hora}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-slate-400">Valor:</span>
                  <span className="text-white">R$ {(selectedBilhete || bilhete)?.valor.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-slate-400">Status:</span>
                  <span className="text-white">
                    {getStatusBadge((selectedBilhete || bilhete)?.status || "pendente")}
                  </span>
                </div>
                {(selectedBilhete || bilhete)?.qr_code_pix && (
                  <div className="mt-3 pt-3 border-t border-slate-600">
                    <span className="text-slate-400 text-sm">QR Code PIX:</span>
                    <div className="bg-slate-600 p-2 rounded mt-1">
                      <p className="text-xs text-slate-300 font-mono break-all">
                        {(selectedBilhete || bilhete)?.qr_code_pix}
                      </p>
                    </div>
                  </div>
                )}
                {(selectedBilhete || bilhete)?.transaction_id && (
                  <div className="flex justify-between text-sm">
                    <span className="text-slate-400">ID Transação:</span>
                    <span className="text-white font-mono text-xs">{(selectedBilhete || bilhete)?.transaction_id}</span>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <h4 className="text-white font-medium">Apostas:</h4>
                <div className="bg-slate-700 p-3 rounded-lg space-y-1">
                  {(selectedBilhete || bilhete)?.apostas.map((aposta, index) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span className="text-slate-300">{aposta.jogo}</span>
                      <span className="text-white font-medium">{aposta.resultado}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Botão de imprimir sempre disponível para bilhetes pagos */}
              {((selectedBilhete && selectedBilhete.status === 'pago') ||
                (!selectedBilhete && bilhete && (bilhete.status === 'pago' || pixData?.status === 'confirmed'))) && (
                <Button
                  onClick={printBilhete}
                  disabled={printingBilhete}
                  className="w-full bg-green-600 hover:bg-green-700 py-3 text-base font-medium"
                >
                  {printingBilhete ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Imprimindo bilhete...
                    </>
                  ) : (
                    <>
                      <Printer className="h-4 w-4 mr-2" />
                      🖨️ Imprimir Bilhete
                    </>
                  )}
                </Button>
              )}

              {/* Mensagem para bilhetes pendentes */}
              {((selectedBilhete && selectedBilhete.status !== 'pago') ||
                (!selectedBilhete && bilhete && bilhete.status !== 'pago' && pixData?.status !== 'confirmed')) && (
                <div className="bg-yellow-600/20 border border-yellow-600/50 rounded-lg p-3 space-y-3">
                  <div className="text-center">
                    <span className="text-yellow-400 text-sm">
                      ⏳ Aguardando confirmação do pagamento para imprimir
                    </span>
                  </div>

                  {/* Botão para confirmar pagamento manualmente */}
                  <div className="text-center">
                    <Button
                      onClick={async () => {
                        const bilheteParaConfirmar = selectedBilhete || bilhete
                        if (!bilheteParaConfirmar || !user?.id) return

                        try {
                          // Primeiro verificar se pode confirmar manualmente
                          const checkResponse = await fetch('/api/payment/check-status', {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                              bilhete_codigo: bilheteParaConfirmar.codigo,
                              user_id: user.id
                            })
                          })

                          if (checkResponse.ok) {
                            const checkResult = await checkResponse.json()

                            if (checkResult.already_paid) {
                              toast.success("✅ Este bilhete já está pago!")
                              // Atualizar estado local
                              if (selectedBilhete) {
                                setSelectedBilhete(prev => prev ? { ...prev, status: 'pago' } : null)
                              }
                              if (bilhete) {
                                setBilhete(prev => prev ? { ...prev, status: 'pago' } : null)
                              }
                              return
                            }

                            if (!checkResult.can_confirm_manually) {
                              toast.error("⏳ Aguarde mais alguns minutos antes de confirmar manualmente")
                              return
                            }
                          }

                          // Confirmar se o usuário realmente pagou
                          const confirmacao = confirm(
                            `⚠️ CONFIRMAÇÃO DE PAGAMENTO\n\n` +
                            `Bilhete: ${bilheteParaConfirmar.codigo}\n` +
                            `Valor: R$ ${bilheteParaConfirmar.valor_total || bilheteParaConfirmar.valor}\n\n` +
                            `Você REALMENTE já pagou este bilhete via PIX?\n\n` +
                            `⚠️ Use esta opção apenas se já pagou e o sistema não detectou automaticamente.`
                          )

                          if (!confirmacao) {
                            return
                          }

                          // Simular webhook de pagamento aprovado
                          const response = await fetch('/api/v1/MP/webhookruntransation', {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                              order_id: bilheteParaConfirmar.codigo,
                              status: 'PAID',
                              type: 'PIXOUT',
                              message: 'Payment confirmed manually by user'
                            })
                          })

                          if (response.ok) {
                            const result = await response.json()
                            if (result.status === 'pago') {
                              // Atualizar estado local
                              if (selectedBilhete) {
                                setSelectedBilhete(prev => prev ? { ...prev, status: 'pago' } : null)
                              }
                              if (bilhete) {
                                setBilhete(prev => prev ? { ...prev, status: 'pago' } : null)
                              }

                              // Atualizar lista de bilhetes do usuário
                              setUserBilhetes(prev =>
                                prev.map(b =>
                                  b.codigo === bilheteParaConfirmar.codigo
                                    ? { ...b, status: 'pago' }
                                    : b
                                )
                              )

                              // Mostrar notificação de sucesso
                              playSuccessSound()
                              setPaymentSuccessData({
                                codigo: bilheteParaConfirmar.codigo,
                                valor: bilheteParaConfirmar.valor_total || bilheteParaConfirmar.valor || "0,00",
                                data: new Date().toLocaleString('pt-BR'),
                                transactionId: bilheteParaConfirmar.transaction_id || bilheteParaConfirmar.codigo
                              })
                              setShowPaymentSuccess(true)

                              toast.success("🎉 Pagamento confirmado manualmente!")
                            } else {
                              toast.error("❌ Erro ao confirmar pagamento")
                            }
                          } else {
                            toast.error("❌ Erro na comunicação com o servidor")
                          }
                        } catch (error) {
                          console.error("Erro ao confirmar pagamento:", error)
                          toast.error("❌ Erro ao confirmar pagamento")
                        }
                      }}
                      variant="outline"
                      size="sm"
                      className="border-yellow-500 text-yellow-400 hover:bg-yellow-500/10 text-xs"
                    >
                      ✅ Já Paguei - Confirmar Manualmente
                    </Button>
                  </div>

                  <div className="text-center text-xs text-yellow-500">
                    Use apenas se já pagou e o sistema não detectou automaticamente
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Payment Success Dialog */}
      {showPaymentSuccess && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm">
          <div className="w-[90vw] max-w-sm sm:max-w-md bg-gradient-to-br from-green-600 to-green-700 border border-green-500 text-white rounded-2xl shadow-2xl relative animate-in fade-in-0 zoom-in-95 duration-300">
            {/* Botão de fechar */}
            <button
              onClick={() => setShowPaymentSuccess(false)}
              className="absolute top-4 right-4 text-white/70 hover:text-white text-xl z-10 w-8 h-8 flex items-center justify-center rounded-full hover:bg-white/10 transition-colors"
            >
              ✕
            </button>
          {/* Animação de confete */}
          <div className="absolute inset-0 pointer-events-none">
            <div className="confetti-animation absolute top-2 left-1/4 w-2 h-2 bg-yellow-300 rounded-full" style={{animationDelay: '0s'}}></div>
            <div className="confetti-animation absolute top-2 right-1/4 w-2 h-2 bg-blue-300 rounded-full" style={{animationDelay: '0.2s'}}></div>
            <div className="confetti-animation absolute top-2 left-1/2 w-2 h-2 bg-red-300 rounded-full" style={{animationDelay: '0.4s'}}></div>
            <div className="confetti-animation absolute top-2 left-3/4 w-2 h-2 bg-purple-300 rounded-full" style={{animationDelay: '0.6s'}}></div>
            <div className="confetti-animation absolute top-2 right-1/3 w-2 h-2 bg-pink-300 rounded-full" style={{animationDelay: '0.8s'}}></div>
          </div>

          <div className="text-center relative z-10 pt-6 pb-4">
            <div className="success-icon mx-auto mb-4 w-16 h-16 sm:w-20 sm:h-20 bg-white rounded-full flex items-center justify-center shadow-lg">
              <span className="text-3xl sm:text-4xl animate-bounce">✅</span>
            </div>
            <h2 className="success-title text-xl sm:text-2xl font-bold text-white mb-2">
              PAGO COM SUCESSO!
            </h2>
            <p className="text-green-100 text-base sm:text-lg font-medium">
              Seu bilhete foi aprovado
            </p>
          </div>

          <div className="space-y-4 sm:space-y-6 py-4 px-2 sm:px-4 relative z-10">
            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 sm:p-6 space-y-3 border border-white/20">
              <div className="payment-info-item flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-2">
                <span className="payment-info-label text-green-100 text-sm sm:text-base font-medium">Código do Bilhete:</span>
                <span className="payment-info-value font-bold text-white font-mono text-sm sm:text-base break-all">
                  {paymentSuccessData?.codigo}
                </span>
              </div>
              <div className="payment-info-item flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-2">
                <span className="payment-info-label text-green-100 text-sm sm:text-base font-medium">Valor Pago:</span>
                <span className="payment-info-value font-bold text-white text-lg sm:text-xl">
                  R$ {paymentSuccessData?.valor}
                </span>
              </div>
              <div className="payment-info-item flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-2">
                <span className="payment-info-label text-green-100 text-sm sm:text-base font-medium">Data/Hora:</span>
                <span className="payment-info-value font-bold text-white text-sm sm:text-base">
                  {paymentSuccessData?.data}
                </span>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 sm:p-6 text-center border border-white/20">
              <div className="text-green-100 text-sm sm:text-base mb-3 font-medium">Status do Bilhete</div>
              <div className="status-badge inline-flex items-center px-4 py-2 sm:px-6 sm:py-3 rounded-full bg-green-500 text-white font-bold text-sm sm:text-base shadow-lg">
                ✅ APROVADO
              </div>
            </div>

            <div className="text-center text-green-100 text-sm sm:text-base font-medium">
              🎯 Boa sorte em suas apostas!
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 pt-4 px-2 sm:px-4">
            <Button
              onClick={() => {
                setShowPaymentSuccess(false)
                setShowUserBilhetes(true)
              }}
              className="success-button flex-1 bg-white text-green-600 hover:bg-green-50 font-bold py-3 text-sm sm:text-base rounded-xl shadow-lg transition-all duration-200 hover:scale-105"
            >
              📋 Ver Meus Bilhetes
            </Button>
            <button
              onClick={() => setShowPaymentSuccess(false)}
              className="success-button flex-1 bg-white text-green-700 hover:bg-green-50 font-bold py-3 text-sm sm:text-base rounded-xl transition-all duration-200 hover:scale-105 shadow-lg"
            >
              ✅ OK
            </button>
          </div>
          </div>
        </div>
      )}
      </div>
    </>
  )
}
