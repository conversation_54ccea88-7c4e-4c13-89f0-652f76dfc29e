import { type NextRequest, NextResponse } from "next/server"
import { executeQuery, executeQuerySingle } from "@/lib/database"

// Força renderização dinâmica
export const dynamic = 'force-dynamic'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cambistaId = parseInt(params.id)

    if (!cambistaId || isNaN(cambistaId)) {
      return NextResponse.json(
        { error: "ID do cambista inválido" },
        { status: 400 }
      )
    }

    console.log("📊 Buscando estatísticas do cambista:", cambistaId)

    // Verificar se o usuário é realmente um cambista
    const cambista = await executeQuerySingle(
      "SELECT id, nome, email, tipo, porcentagem_comissao FROM usuarios WHERE id = ? AND tipo = 'cambista'",
      [cambistaId]
    )

    if (!cambista) {
      return NextResponse.json(
        { error: "Cambista não encontrado" },
        { status: 404 }
      )
    }

    // Buscar estatísticas gerais
    const statsQuery = `
      SELECT 
        COUNT(*) as bilhetes_vendidos,
        COALESCE(SUM(valor_total), 0) as valor_total_vendas,
        COALESCE(SUM(CASE WHEN status = 'pago' THEN 1 ELSE 0 END), 0) as bilhetes_pagos,
        COALESCE(SUM(CASE WHEN status = 'pendente' THEN 1 ELSE 0 END), 0) as bilhetes_pendentes
      FROM bilhetes 
      WHERE cambista_id = ?
    `

    const stats = await executeQuerySingle(statsQuery, [cambistaId])

    // Buscar vendas de hoje
    const vendasHojeQuery = `
      SELECT
        COALESCE(COUNT(*), 0) as vendas_hoje,
        COALESCE(SUM(valor_total), 0) as valor_vendas_hoje
      FROM bilhetes
      WHERE cambista_id = ?
      AND DATE(created_at) = CURDATE()
    `

    const vendasHoje = await executeQuerySingle(vendasHojeQuery, [cambistaId])

    // Calcular comissões
    const porcentagemComissao = cambista.porcentagem_comissao || 10
    const comissaoTotal = (stats.valor_total_vendas * porcentagemComissao) / 100
    const comissaoHoje = (vendasHoje.valor_vendas_hoje * porcentagemComissao) / 100

    const resultado = {
      bilhetes_vendidos: parseInt(stats.bilhetes_vendidos) || 0,
      valor_total_vendas: parseFloat(stats.valor_total_vendas) || 0,
      bilhetes_pagos: parseInt(stats.bilhetes_pagos) || 0,
      bilhetes_pendentes: parseInt(stats.bilhetes_pendentes) || 0,
      vendas_hoje: parseInt(vendasHoje.vendas_hoje) || 0,
      comissao_total: comissaoTotal,
      comissao_hoje: comissaoHoje,
      porcentagem_comissao: porcentagemComissao
    }

    console.log("✅ Estatísticas calculadas:", resultado)

    return NextResponse.json(resultado)

  } catch (error: any) {
    console.error("❌ Erro ao buscar estatísticas do cambista:", error)
    
    return NextResponse.json(
      { 
        error: "Erro interno do servidor",
        message: "Não foi possível buscar as estatísticas"
      },
      { status: 500 }
    )
  }
}
