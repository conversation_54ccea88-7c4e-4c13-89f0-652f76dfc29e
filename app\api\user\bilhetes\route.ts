import { NextRequest, NextResponse } from "next/server"
import { initializeDatabase, executeQuery } from "@/lib/database-config"

export const dynamic = 'force-dynamic'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get("user_id")

    console.log("🎫 Buscando bilhetes para user_id:", userId)

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: "user_id é obrigatório"
      }, { status: 400 })
    }

    try {
      await initializeDatabase()

      // Buscar bilhetes do usuário com timeout
      const bilhetes = await Promise.race([
        executeQuery(`
          SELECT
            b.id,
            b.codigo,
            b.usuario_id,
            b.usuario_nome,
            b.usuario_email,
            b.valor_total,
            b.quantidade_apostas,
            b.status,
            b.transaction_id,
            b.created_at
          FROM bilhetes b
          WHERE b.usuario_id = ?
          ORDER BY b.created_at DESC
          LIMIT 50
        `, [userId]),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Timeout na consulta de bilhetes')), 5000)
        )
      ])

      console.log("📊 Bilhetes encontrados:", bilhetes?.length || 0)

      // Formatar os dados para o frontend
      const bilhetesFormatados = (bilhetes || []).map((bilhete: any) => {
        return {
          id: "BLT" + bilhete.id,
          codigo: bilhete.codigo,
          transaction_id: bilhete.transaction_id || bilhete.codigo, // Usar transaction_id real ou fallback para codigo
          data: new Date(bilhete.created_at).toLocaleDateString('pt-BR'),
          hora: new Date(bilhete.created_at).toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit'
          }),
          apostas: [],
          valor: parseFloat(bilhete.valor_total),
          status: bilhete.status,
          premio: 0,
          qr_code_pix: "",
          db_id: bilhete.id
        }
      })

      console.log(`✅ ${bilhetesFormatados.length} bilhetes formatados para usuário ${userId}`)

      return NextResponse.json({
        success: true,
        bilhetes: bilhetesFormatados,
        total: bilhetesFormatados.length,
        source: 'database'
      })

    } catch (dbError) {
      console.warn(`⚠️ Erro ao acessar banco, retornando dados básicos:`, dbError.message)

      // Retornar dados básicos sem erro
      return NextResponse.json({
        success: true,
        bilhetes: [],
        total: 0,
        source: 'fallback'
      })
    }

  } catch (error) {
    console.error("❌ Erro ao buscar bilhetes:", error)
    return NextResponse.json({
      success: false,
      error: "Erro interno do servidor",
      message: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
