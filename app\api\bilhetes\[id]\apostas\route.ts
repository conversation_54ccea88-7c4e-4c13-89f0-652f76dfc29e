import { NextRequest, NextResponse } from "next/server"
import mysql from 'mysql2/promise'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  let connection
  try {
    const bilheteId = params.id

    console.log("🎫 Buscando apostas do bilhete:", bilheteId)

    // Criar conexão direta
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'sistema-bolao-top'
    })

    // Buscar apostas do bilhete com nomes reais dos times
    const [apostas] = await connection.execute(`
      SELECT 
        a.id,
        a.resultado,
        j.id as jogo_id,
        tc.nome as time_casa,
        tc.nome_curto as time_casa_curto,
        tc.logo_url as time_casa_logo,
        tf.nome as time_fora,
        tf.nome_curto as time_fora_curto,
        tf.logo_url as time_fora_logo,
        j.data_jogo,
        j.status as jogo_status,
        c.nome as campeonato
      FROM apostas a
      LEFT JOIN jogos j ON a.jogo_id = j.id
      LEFT JOIN times tc ON j.time_casa_id = tc.id
      LEFT JOIN times tf ON j.time_fora_id = tf.id
      LEFT JOIN campeonatos c ON j.campeonato_id = c.id
      WHERE a.bilhete_id = ?
      ORDER BY a.id ASC
    `, [bilheteId])

    console.log(`📊 Encontradas ${apostas.length} apostas para o bilhete ${bilheteId}`)

    // Formatar apostas para impressão
    const apostasFormatadas = apostas.map(aposta => ({
      id: aposta.id,
      jogo: `${aposta.time_casa || 'Time Casa'} x ${aposta.time_fora || 'Time Fora'}`,
      resultado: aposta.resultado === 'casa' ? 'Casa' : 
                aposta.resultado === 'empate' ? 'Empate' : 'Fora',
      time_casa: aposta.time_casa,
      time_fora: aposta.time_fora,
      time_casa_logo: aposta.time_casa_logo,
      time_fora_logo: aposta.time_fora_logo,
      campeonato: aposta.campeonato,
      data_jogo: aposta.data_jogo,
      status: aposta.jogo_status
    }))

    return NextResponse.json({
      success: true,
      apostas: apostasFormatadas,
      total: apostasFormatadas.length
    })

  } catch (error) {
    console.error("❌ Erro ao buscar apostas do bilhete:", error)
    
    // Fallback com dados de exemplo
    const apostasExemplo = [
      {
        id: 1,
        jogo: "Atlético Mineiro x Cruzeiro",
        resultado: "Casa",
        time_casa: "Atlético Mineiro",
        time_fora: "Cruzeiro"
      },
      {
        id: 2,
        jogo: "Fortaleza EC x EC Bahia",
        resultado: "Casa",
        time_casa: "Fortaleza EC",
        time_fora: "EC Bahia"
      },
      {
        id: 3,
        jogo: "CR Vasco da Gama x Grêmio",
        resultado: "Empate",
        time_casa: "CR Vasco da Gama",
        time_fora: "Grêmio"
      }
    ]

    return NextResponse.json({
      success: true,
      apostas: apostasExemplo,
      total: apostasExemplo.length,
      fallback: true
    })

  } finally {
    if (connection) {
      try {
        await connection.end()
      } catch (e) {
        console.log("Erro ao fechar conexão:", e.message)
      }
    }
  }
}
